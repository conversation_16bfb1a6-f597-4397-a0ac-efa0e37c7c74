package com.bxm.customer.domain.vo.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 保存状态请求VO
 * 
 * 用于待交付、待扣款状态的保存操作
 * targetStatus将存储到processing_status字段中
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("保存状态请求VO")
public class SaveStatusReqVO {

    /**
     * 交付单编号
     */
    @NotBlank(message = "交付单编号不能为空")
    @Size(max = 50, message = "交付单编号长度不能超过50个字符")
    @ApiModelProperty(value = "交付单编号", required = true, example = "VAD2508051430001A1C")
    private String deliveryOrderNo;

    /**
     * 目标状态
     * 将存储到processing_status字段中
     */
    @NotBlank(message = "目标状态不能为空")
    @Size(max = 50, message = "目标状态长度不能超过50个字符")
    @ApiModelProperty(value = "目标状态，存储到processing_status字段", required = true, 
                     example = "SUBMITTED_PENDING_DELIVERY", 
                     notes = "适用于待交付、待扣款等状态")
    private String targetStatus;

    /**
     * 总扣缴额（可选）
     */
    @DecimalMin(value = "0.00", message = "总扣缴额不能为负数")
    @ApiModelProperty(value = "总扣缴额", example = "1000.00", notes = "可选字段，用于扣款相关状态")
    private BigDecimal totalWithholdingAmount;

    /**
     * 备注信息
     */
    @Size(max = 1000, message = "备注信息长度不能超过1000个字符")
    @ApiModelProperty(value = "备注信息", example = "状态保存备注")
    private String remark;
}
