package com.bxm.customer.service.strategy.valueadded;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.mapper.ValueAddedEmployeeMapper;
import com.bxm.customer.service.strategy.AbstractValueAddedEmployeeUpsertStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 国税账号业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class NationalTaxAccountUpsertStrategy extends AbstractValueAddedEmployeeUpsertStrategy {

    @Autowired
    private ValueAddedEmployeeMapper valueAddedEmployeeMapper;

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.NATIONAL_TAX_ACCOUNT.getCode();
    }

    @Override
    public void validateBusinessFields(ValueAddedEmployee employee) {
        // 验证操作类型是否适用于国税账号业务
        if (!ValueAddedOperationType.isValidForBizType(ValueAddedBizType.NATIONAL_TAX_ACCOUNT, employee.getOperationType())) {
            throw new IllegalArgumentException("国税账号业务不支持的操作类型: " + employee.getOperationType());
        }

        // 验证国税账号业务必填字段（格式校验已在VO层完成）
        if (StringUtils.isEmpty(employee.getTaxNumber())) {
            throw new IllegalArgumentException("税号/统一信用代码不能为空");
        }

        if (StringUtils.isEmpty(employee.getQueryPassword())) {
            throw new IllegalArgumentException("登录密码不能为空");
        }

    }

    @Override
    protected void doPreprocess(ValueAddedEmployee employee) {
        // 标准化税号（去除空格，转大写）
        if (StringUtils.isNotEmpty(employee.getTaxNumber())) {
            employee.setTaxNumber(employee.getTaxNumber().trim().toUpperCase());
        }

        // 处理扩展信息：构建国税账号业务的扩展信息
        Map<String, Object> extendInfo = parseExtendInfo(employee.getExtendInfo());

        // 设置默认登录方式（如果未指定）
        if (!extendInfo.containsKey("loginMethod")) {
            extendInfo.put("loginMethod", "账号密码登录");
        }

        // 构建业务类型扩展信息
        ValueAddedOperationType operationType = getOperationTypeByCode(employee.getOperationType());
        if (operationType != null) {
            Map<String, Object> bizTypeExtendInfo = buildBizTypeExtendInfo(
                    ValueAddedBizType.NATIONAL_TAX_ACCOUNT,
                    operationType
            );
            extendInfo.putAll(bizTypeExtendInfo);
        }

        // 记录操作类型的中文描述
        String operationTypeName = getOperationTypeName(employee.getOperationType());
        extendInfo.put("operationTypeName", operationTypeName);

        // 将扩展信息序列化回JSON字符串
        employee.setExtendInfo(serializeExtendInfo(extendInfo));

        log.info("National tax account employee preprocessed: Name={}, TaxNumber={}, OperationType={}",
                employee.getEmployeeName(), maskSensitiveInfo(employee.getTaxNumber()), operationTypeName);
    }

    @Override
    protected void doPostprocess(ValueAddedEmployee employee, boolean isUpdate) {
        String operation = isUpdate ? "updated" : "created";
        String operationTypeName = getOperationTypeName(employee.getOperationType());

        log.info("National tax account employee {} successfully: ID={}, Name={}, IdNumber={}, Mobile={}, TaxNumber={}, Operation={}, DeliveryOrderNo={}",
                operation, employee.getId(), employee.getEmployeeName(),
                maskSensitiveInfo(employee.getIdNumber()), maskSensitiveInfo(employee.getMobile()),
                maskSensitiveInfo(employee.getTaxNumber()), operationTypeName, employee.getDeliveryOrderNo());

        // 国税账号业务的后处理逻辑
        // 这里可以添加具体的业务逻辑，如：
        // 2. 记录操作日志
    }

    @Override
    public ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 国税账号业务的唯一性判断：交付单编号 + 身份证号 + 业务类型
        // 也可以考虑税号的唯一性
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, employee.getDeliveryOrderNo())
                .eq(ValueAddedEmployee::getIdNumber, employee.getIdNumber())
                .eq(ValueAddedEmployee::getBizType, ValueAddedBizType.NATIONAL_TAX_ACCOUNT.getCode());

        return valueAddedEmployeeMapper.selectOne(queryWrapper);
    }

    @Override
    protected void doMerge(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 更新国税账号特定字段
        if (StringUtils.isNotEmpty(newEmployee.getTaxNumber())) {
            existing.setTaxNumber(newEmployee.getTaxNumber());
        }
        if (StringUtils.isNotEmpty(newEmployee.getQueryPassword())) {
            existing.setQueryPassword(newEmployee.getQueryPassword());
        }

        // 合并扩展信息
        Map<String, Object> existingExtendInfo = parseExtendInfo(existing.getExtendInfo());
        Map<String, Object> newExtendInfo = parseExtendInfo(newEmployee.getExtendInfo());

        // 合并扩展信息，新信息覆盖旧信息
        existingExtendInfo.putAll(newExtendInfo);

        // 更新合并时间戳
        existingExtendInfo.put("lastMergeTimestamp", System.currentTimeMillis());

        // 序列化合并后的扩展信息
        existing.setExtendInfo(serializeExtendInfo(existingExtendInfo));

        log.info("National tax account employee merged: ID={}, Name={}, TaxNumber={}",
                existing.getId(), existing.getEmployeeName(), existing.getTaxNumber());
    }



    /**
     * 将扩展信息Map序列化为JSON字符串
     *
     * @param extendInfo 扩展信息Map
     * @return JSON字符串
     */
    private String serializeExtendInfo(Map<String, Object> extendInfo) {
        if (extendInfo == null || extendInfo.isEmpty()) {
            return null;
        }

        try {
            return JSON.toJSONString(extendInfo);
        } catch (Exception e) {
            log.error("Failed to serialize extend info to JSON: {}, error: {}", extendInfo, e.getMessage());
            return null;
        }
    }

    /**
     * 根据操作类型代码获取对应的枚举
     *
     * @param operationTypeCode 操作类型代码
     * @return 操作类型枚举
     */
    private ValueAddedOperationType getOperationTypeByCode(Integer operationTypeCode) {
        if (operationTypeCode == null) {
            return null;
        }

        switch (operationTypeCode) {
            case 1:
                return ValueAddedOperationType.ACCOUNTING_REAL_NAME;
            case 2:
                return ValueAddedOperationType.REMOTE_REAL_NAME;
            default:
                return null;
        }
    }

    /**
     * 根据操作类型代码获取中文名称
     *
     * @param operationType 操作类型代码
     * @return 操作类型中文名称
     */
    private String getOperationTypeName(Integer operationType) {
        ValueAddedOperationType operationTypeEnum = getOperationTypeByCode(operationType);
        if (operationTypeEnum != null) {
            return operationTypeEnum.getName();
        }

        return operationType != null ? "未知操作(" + operationType + ")" : "未知操作";
    }

    /**
     * 脱敏敏感信息用于日志输出
     *
     * @param sensitiveInfo 敏感信息
     * @return 脱敏后的信息
     */
    private String maskSensitiveInfo(String sensitiveInfo) {
        if (StringUtils.isEmpty(sensitiveInfo)) {
            return sensitiveInfo;
        }

        if (sensitiveInfo.length() <= 4) {
            return "****";
        }

        // 保留前2位和后2位，中间用*替代
        return sensitiveInfo.substring(0, 2) + "****" + sensitiveInfo.substring(sensitiveInfo.length() - 2);
    }

}
