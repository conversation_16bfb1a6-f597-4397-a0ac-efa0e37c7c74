<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CCustomerServiceMapper">
    
    <resultMap type="com.bxm.customer.domain.CCustomerService" id="CCustomerServiceResult">
        <result property="id"    column="id"    />
        <result property="businessDeptId"    column="business_dept_id"    />
        <result property="businessTopDeptId"    column="business_top_dept_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="customerCompanyName"    column="customer_company_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="taxNumber"    column="tax_number"    />
        <result property="serviceNumber"    column="service_number"    />
        <result property="firstAccountPeriod"    column="first_account_period"    />
        <result property="endAccountPeriod"    column="end_account_period"    />
        <result property="taxType"    column="tax_type"    />
        <result property="advisorDeptId"    column="advisor_dept_id"    />
        <result property="accountingDeptId"    column="accounting_dept_id"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCCustomerServiceVo">
        select id, business_dept_id, business_top_dept_id, customer_name, customer_company_name, credit_code, tax_number, service_number, first_account_period, end_account_period, tax_type, advisor_dept_id, accounting_dept_id, is_del, create_by, create_time, update_by, update_time from c_customer_service
    </sql>

    <select id="selectCCustomerServiceList" parameterType="com.bxm.customer.domain.CCustomerService" resultMap="CCustomerServiceResult">
        <include refid="selectCCustomerServiceVo"/>
        <where>  
            <if test="businessDeptId != null "> and business_dept_id = #{businessDeptId}</if>
            <if test="businessTopDeptId != null "> and business_top_dept_id = #{businessTopDeptId}</if>
            <if test="serviceDeptId != null "> and service_dept_id = #{serviceDeptId}</if>
            <if test="serviceTopDeptId != null "> and service_top_dept_id = #{serviceTopDeptId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="customerCompanyName != null  and customerCompanyName != ''"> and customer_company_name like concat('%', #{customerCompanyName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="taxNumber != null  and taxNumber != ''"> and tax_number = #{taxNumber}</if>
            <if test="serviceNumber != null  and serviceNumber != ''"> and service_number = #{serviceNumber}</if>
            <if test="firstAccountPeriod != null "> and first_account_period = #{firstAccountPeriod}</if>
            <if test="endAccountPeriod != null "> and end_account_period = #{endAccountPeriod}</if>
            <if test="taxType != null "> and tax_type = #{taxType}</if>
            <if test="advisorDeptId != null "> and advisor_dept_id = #{advisorDeptId}</if>
            <if test="accountingDeptId != null "> and accounting_dept_id = #{accountingDeptId}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectCCustomerServiceById" parameterType="Long" resultMap="CCustomerServiceResult">
        <include refid="selectCCustomerServiceVo"/>
        where id = #{id}
    </select>
    <select id="customerServiceList" resultType="com.bxm.customer.domain.dto.CustomerServiceDTO">
        select
        ccs.id as id,
        ccs.customer_name as customerName,
        ccs.customer_company_name as customerCompanyName,
        ccs.credit_code as creditCode,
        ccs.service_number as serviceNumber,
        ccs.tax_type as taxType,
        ccs.service_status as serviceStatus,
        ccs.first_account_period as startPeriod,
        ccs.end_account_period as endPeriod,
        ccs.this_month_income as thisMonthIncome,
        ccs.this_month_income_rpa_result as thisMonthIncomeRpaResult,
        ccs.this_season_income as thisSeasonIncome,
        ccs.this_season_income_rpa_result as thisSeasonIncomeRpaResult,
        ccs.this_12_month_income as this12MonthIncome,
        ccs.this_12_month_income_rpa_result as this12MonthIncomeRpaResult,
        ccs.this_year_income as thisYearIncome,
        ccs.this_year_income_rpa_result as thisYearIncomeRpaResult,
        ccs.business_dept_id as businessDeptId,
        sd.dept_name as businessDeptName,
        ccs.advisor_dept_id as advisorDeptId,
        sd1.dept_name as advisorDeptName,
        ccs.accounting_dept_id as accountingDeptId,
        sd2.dept_name as accountingDeptName,
        ccs.ticket_time as ticketTime,
        ccs.business_top_dept_id as businessTopDeptId,
        ccs.accounting_top_dept_id as accountingTopDeptId,
        ccs.accounting_remark as accountingRemark,
        ccs.advisor_remark as advisorRemark,
        cca.profit_get_time as profitGetTime,
        cca.period as lastInAccountPeriod,
        cca.major_income_total as mainIncome,
        cca.major_cost_total as mainCost,
        cca.profit_total as profit,
        cca.prior_year_expense_increase as priorYearExpenseIncrease,
        cca.tax_report_count as taxReportCount,
        cca.tax_report_salary_total as taxReportSalaryTotal,
        ccs.create_time as createTime
        from c_customer_service ccs
        left join c_customer_service_cashier_accounting cca on ccs.last_in_account_id = cca.id and cca.is_del = 0
        left join sys_dept sd on ccs.business_dept_id = sd.dept_id
        left join sys_dept sd1 on ccs.advisor_dept_id = sd1.dept_id
        left join sys_dept sd2 on ccs.accounting_dept_id = sd2.dept_id
        <where>
            ccs.is_del = 0
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (ccs.customer_name like concat('%', #{vo.keyWord}, '%') or ccs.customer_company_name like concat('%', #{vo.keyWord}, '%')
                or ccs.credit_code like concat('%', #{vo.keyWord}, '%'))
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and ccs.customer_name like concat('%', #{vo.customerName}, '%')
            </if>
            <if test="vo.customerCompanyName != null and vo.customerCompanyName != ''">
                and ccs.customer_company_name like concat('%', #{vo.customerCompanyName}, '%')
            </if>
            <if test="vo.serviceNumber != null and vo.serviceNumber != ''">
                and ccs.service_number like concat('%', #{vo.serviceNumber}, '%')
            </if>
            <if test="vo.taxType != null">
                and ccs.tax_type = #{vo.taxType}
            </if>
            <if test="vo.serviceStatus != null">
                and ccs.service_status = #{vo.serviceStatus}
            </if>
            <if test="vo.ticketTimeStart != null and vo.ticketTimeStart != ''">
                and ccs.ticket_time &gt;= #{vo.ticketTimeStart}
            </if>
            <if test="vo.ticketTimeEnd != null and vo.ticketTimeEnd != ''">
                and ccs.ticket_time &lt;= #{vo.ticketTimeEnd}
            </if>
            <if test="isAdmin != null and isAdmin == 0">
                <if test="deptType != null and deptType == 1 and deptIds != null and deptIds.size > 0">
                    and (ccs.advisor_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="deptType != null and deptType == 2 and deptIds != null and deptIds.size > 0">
                    and (ccs.accounting_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.queryDeptIds != null and vo.queryDeptIds.size > 0">
                <if test="deptType != null and deptType == 1">
                    and (ccs.advisor_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="deptType != null and deptType == 2">
                    and (ccs.accounting_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.advisorSearchDeptIds != null and vo.advisorSearchDeptIds.size > 0">
                and (ccs.advisor_dept_id in
                <foreach collection="vo.advisorSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or ccs.advisor_top_dept_id in
                <foreach collection="vo.advisorSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>)
            </if>
            <if test="vo.accountingSearchDeptIds != null and vo.accountingSearchDeptIds.size > 0">
                and (ccs.accounting_dept_id in
                <foreach collection="vo.accountingSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or ccs.accounting_top_dept_id in
                <foreach collection="vo.accountingSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>)
            </if>
            <if test="vo.tagName != null and vo.tagName != ''">
                <if test="tagIncludeFlag != null and tagIncludeFlag == 1 and ids != null and ids.size > 0">
                    and ccs.id in
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="tagIncludeFlag != null and tagIncludeFlag == 0 and ids != null and ids.size > 0">
                    and ccs.id not in
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="vo.businessDeptId != null">
                and ccs.business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.accountingTopDeptId != null">
                and ccs.accounting_top_dept_id = #{vo.accountingTopDeptId}
            </if>
            <if test="vo.businessDeptIdList != null and vo.businessDeptIdList != ''">
                and ccs.business_dept_id in (${vo.businessDeptIdList})
            </if>
            <if test="vo.accountingTopDeptIdList != null and vo.accountingTopDeptIdList != ''">
                and ccs.accounting_top_dept_id in (${vo.accountingTopDeptIdList})
            </if>
            <if test="vo.startPeriodStart != null">
                and ccs.first_account_period >= #{vo.startPeriodStart}
            </if>
            <if test="vo.startPeriodEnd != null">
                and ccs.first_account_period &lt;= #{vo.startPeriodEnd}
            </if>
            <if test="vo.endPeriodStart != null">
                and ccs.end_account_period >= #{vo.endPeriodStart}
            </if>
            <if test="vo.endPeriodEnd != null">
                and ccs.end_account_period &lt;= #{vo.endPeriodEnd}
            </if>
            <if test="vo.advisorDeptId != null">
                and ccs.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and ccs.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                and ccs.id in
                <foreach collection="customerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.profitGetTimeStart != null and vo.profitGetTimeStart != ''">
                and cca.profit_get_time &gt;= #{vo.profitGetTimeStart}
            </if>
            <if test="vo.profitGetTimeEnd != null and vo.profitGetTimeEnd != ''">
                and cca.profit_get_time &lt;= #{vo.profitGetTimeEnd}
            </if>
        </where>
        <if test="vo.sortBy != null and vo.sortBy != ''">
            order by ${vo.sortBy} ${vo.sortType}, ccs.id desc
        </if>
        <if test="vo.sortBy == null or vo.sortBy == ''">
            order by ccs.id desc
        </if>
    </select>
    <select id="customerServiceListByCreditCode" resultType="com.bxm.customer.domain.dto.CustomerServiceDTO">
        select
        ccs.id as id,
        ccs.customer_name as customerName,
        ccs.customer_company_name as customerCompanyName,
        ccs.credit_code as creditCode,
        ccs.service_number as serviceNumber,
        ccs.tax_type as taxType,
        ccs.service_status as serviceStatus,
        ccs.first_account_period as startPeriod,
        ccs.end_account_period as endPeriod,
        ccs.business_dept_id as businessDeptId,
        sd.dept_name as businessDeptName,
        ccs.advisor_dept_id as advisorDeptId,
        sd1.dept_name as advisorDeptName,
        ccs.accounting_dept_id as accountingDeptId,
        sd2.dept_name as accountingDeptName,
        ccs.business_top_dept_id as businessTopDeptId,
        ccs.accounting_top_dept_id as accountingTopDeptId,
        sd3.dept_name as advisorTopDeptName
        from c_customer_service ccs
        left join sys_dept sd on ccs.business_dept_id = sd.dept_id
        left join sys_dept sd1 on ccs.advisor_dept_id = sd1.dept_id
        left join sys_dept sd2 on ccs.accounting_dept_id = sd2.dept_id
        left join sys_dept sd3 on ccs.advisor_top_dept_id = sd3.dept_id
        where
        ccs.is_del = 0 and ccs.credit_code = #{creditCode}
        order by ccs.id desc
    </select>
    <select id="customerServiceWarningListByAccountingDept"
            resultType="com.bxm.customer.domain.dto.CustomerServiceIncomeExcessDTO">
        select
        ccs.id,
        ccs.customer_name as customerName,
        ccs.customer_company_name as customerCompanyName,
        ccs.business_dept_id as businessDeptId,
        ccs.credit_code as creditCode,
        ccs.tax_type as taxType,
        ccs.service_status as serviceStatus,
        ccs.end_account_period as endPeriod,
        ccs.this_season_income as thisSeasonIncome,
        ccs.this_12_month_income as this12MonthIncome,
        ccs.ticket_time as ticketTime
        from c_customer_service ccs
        <where>
            ccs.this_season_income_rpa_result != 2 and ccs.this_12_month_income_rpa_result != 2 and ccs.tax_type = 1 and ccs.is_del = 0 and (ccs.end_account_period is null or ccs.end_account_period &gt;= #{nowPeriod}) and (ccs.this_season_income >= #{seasonExcessYellowLevel} or ccs.this_12_month_income >= #{yearExcessYellowLevel})
            <if test="keyWord != null and keyWord != ''">
                and (ccs.customer_name like concat('%', #{keyWord}, '%') or ccs.customer_company_name like concat('%', #{keyWord}, '%') or ccs.credit_code like concat('%', #{keyWord}, '%'))
            </if>
            <if test="tagName != null and tagName != ''">
                <if test="tagIncludeType != null and tagIncludeType == 1 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccs.id in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="tagIncludeType != null and tagIncludeType == 0 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccs.id not in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                <if test="deptType == 1">
                    and (ccs.advisor_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="deptType == 2">
                    and (ccs.accounting_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="advisorSearchDeptIds != null and advisorSearchDeptIds.size > 0">
                and (ccs.advisor_dept_id in
                <foreach collection="advisorSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or ccs.advisor_top_dept_id in
                <foreach collection="advisorSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>)
            </if>
            <if test="accountingSearchDeptIds != null and accountingSearchDeptIds.size > 0">
                and (ccs.accounting_dept_id in
                <foreach collection="accountingSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or ccs.accounting_top_dept_id in
                <foreach collection="accountingSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>)
            </if>
            <if test="deptIds != null and deptIds.size > 0">
                <if test="deptType == 1">
                    and (ccs.advisor_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="deptType == 2">
                    and (ccs.accounting_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
        </where>
        order by ccs.id desc
    </select>
    <select id="countCustomerServiceWarningListByAccountingDept" resultType="java.lang.Long">
        select
        count(*) from c_customer_service ccs
        <where>
            ccs.this_season_income_rpa_result != 2 and ccs.this_12_month_income_rpa_result != 2 and (ccs.end_account_period is null or ccs.end_account_period &gt;= #{nowPeriod}) and ccs.tax_type = 1 and ccs.is_del = 0 and (ccs.this_season_income >= #{seasonExcessYellowLevel} or ccs.this_12_month_income >= #{yearExcessYellowLevel})
            <if test="keyWord != null and keyWord != ''">
                and (ccs.customer_name like concat('%', #{keyWord}, '%') or ccs.customer_company_name like concat('%', #{keyWord}, '%') or ccs.credit_code like concat('%', #{keyWord}, '%'))
            </if>
            <if test="tagName != null and tagName != ''">
                <if test="tagIncludeType != null and tagIncludeType == 1 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccs.id in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="tagIncludeType != null and tagIncludeType == 0 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccs.id not in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                <if test="deptType == 1">
                    and (ccs.advisor_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="deptType == 2">
                    and (ccs.accounting_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="searchDeptIds != null and searchDeptIds.size > 0">
                <if test="deptType == 1">
                    and (ccs.accounting_dept_id in
                    <foreach collection="searchDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="searchDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="deptType == 2">
                    and (ccs.advisor_dept_id in
                    <foreach collection="searchDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="searchDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="deptIds != null and deptIds.size > 0">
                <if test="deptType == 1">
                    and (ccs.advisor_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="deptType == 2">
                    and (ccs.accounting_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
        </where>
    </select>


    <insert id="insertCCustomerService" parameterType="com.bxm.customer.domain.CCustomerService" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessDeptId != null">business_dept_id,</if>
            <if test="businessTopDeptId != null">business_top_dept_id,</if>
            <if test="serviceDeptId != null">service_dept_id,</if>
            <if test="serviceTopDeptId != null">service_top_dept_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="customerCompanyName != null and customerCompanyName != ''">customer_company_name,</if>
            <if test="creditCode != null and creditCode != ''">credit_code,</if>
            <if test="taxNumber != null and taxNumber != ''">tax_number,</if>
            <if test="serviceNumber != null and serviceNumber != ''">service_number,</if>
            <if test="firstAccountPeriod != null">first_account_period,</if>
            <if test="endAccountPeriod != null">end_account_period,</if>
            <if test="taxType != null">tax_type,</if>
            <if test="advisorDeptId != null">advisor_dept_id,</if>
            <if test="accountingDeptId != null">accounting_dept_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessDeptId != null">#{businessDeptId},</if>
            <if test="businessTopDeptId != null">#{businessTopDeptId},</if>
            <if test="serviceDeptId != null">#{serviceDeptId},</if>
            <if test="serviceTopDeptId != null">#{serviceTopDeptId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="customerCompanyName != null and customerCompanyName != ''">#{customerCompanyName},</if>
            <if test="creditCode != null and creditCode != ''">#{creditCode},</if>
            <if test="taxNumber != null and taxNumber != ''">#{taxNumber},</if>
            <if test="serviceNumber != null and serviceNumber != ''">#{serviceNumber},</if>
            <if test="firstAccountPeriod != null">#{firstAccountPeriod},</if>
            <if test="endAccountPeriod != null">#{endAccountPeriod},</if>
            <if test="taxType != null">#{taxType},</if>
            <if test="advisorDeptId != null">#{advisorDeptId},</if>
            <if test="accountingDeptId != null">#{accountingDeptId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCCustomerService" parameterType="com.bxm.customer.domain.CCustomerService">
        update c_customer_service
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessDeptId != null">business_dept_id = #{businessDeptId},</if>
            <if test="businessTopDeptId != null">business_top_dept_id = #{businessTopDeptId},</if>
            <if test="serviceDeptId != null">service_dept_id = #{serviceDeptId},</if>
            <if test="serviceTopDeptId != null">service_top_dept_id = #{serviceTopDeptId},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="customerCompanyName != null and customerCompanyName != ''">customer_company_name = #{customerCompanyName},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="taxNumber != null and taxNumber != ''">tax_number = #{taxNumber},</if>
            <if test="serviceNumber != null and serviceNumber != ''">service_number = #{serviceNumber},</if>
            <if test="firstAccountPeriod != null">first_account_period = #{firstAccountPeriod},</if>
            <if test="endAccountPeriod != null">end_account_period = #{endAccountPeriod},</if>
            <if test="taxType != null">tax_type = #{taxType},</if>
            <if test="advisorDeptId != null">advisor_dept_id = #{advisorDeptId},</if>
            <if test="accountingDeptId != null">accounting_dept_id = #{accountingDeptId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateCustomerServiceIncome">
        update c_customer_service ccs
        left join (select customer_service_id, ifnull(sum(all_ticket_amount + no_ticket_income_amount), 0) as total_amount,max(rpa_result) as rpa_result
        from c_customer_service_period_month_income
        where period = #{thisMonth}
        group by customer_service_id) thisMonthIncomeTbl on ccs.id = thisMonthIncomeTbl.customer_service_id
        left join (select customer_service_id, ifnull(sum(all_ticket_amount + no_ticket_income_amount), 0) as total_amount,max(rpa_result) as rpa_result
        from c_customer_service_period_month_income
        where period &gt;= #{thisYearStart} and period &lt;= #{thisYearEnd}
        group by customer_service_id) thisYearIncomeTbl on ccs.id = thisYearIncomeTbl.customer_service_id
        left join (select customer_service_id, ifnull(sum(all_ticket_amount + no_ticket_income_amount), 0) as total_amount,max(rpa_result) as rpa_result
        from c_customer_service_period_month_income
        where period &gt;= #{thisSeasonStart} and period &lt;= #{thisSeasonEnd}
        group by customer_service_id) thisSeasonIncomeTbl on ccs.id = thisSeasonIncomeTbl.customer_service_id
        left join (select customer_service_id, ifnull(sum(all_ticket_amount + no_ticket_income_amount), 0) as total_amount,max(rpa_result) as rpa_result
        from c_customer_service_period_month_income
        where period &gt;= #{this12MonthStart} and period &lt;= #{this12MonthEnd}
        group by customer_service_id) this12MonthIncomeTbl on ccs.id = this12MonthIncomeTbl.customer_service_id
        set ccs.this_month_income = ifnull(thisMonthIncomeTbl.total_amount,0),
        ccs.this_month_income_rpa_result = ifnull(thisMonthIncomeTbl.rpa_result,1),
        ccs.this_year_income = ifnull(thisYearIncomeTbl.total_amount,0),
        ccs.this_year_income_rpa_result = ifnull(thisYearIncomeTbl.rpa_result,1),
        ccs.this_season_income = ifnull(thisSeasonIncomeTbl.total_amount,0),
        ccs.this_season_income_rpa_result = ifnull(thisSeasonIncomeTbl.rpa_result,1),
        ccs.this_12_month_income = ifnull(this12MonthIncomeTbl.total_amount,0),
        ccs.this_12_month_income_rpa_result = ifnull(this12MonthIncomeTbl.rpa_result,1)
        where ccs.is_del = 0
        <if test="customerServiceId != null">
            and ccs.id = #{customerServiceId}
        </if>
    </update>
    <update id="updateCustomerServiceThisMonthIncome">
        update c_customer_service ccs join (select customer_service_id, ifnull(sum(all_ticket_amount + no_ticket_income_amount), 0) as total_amount,max(rpa_result) as rpa_result
            from c_customer_service_period_month_income
            where period = #{thisMonth}
            group by customer_service_id) thisMonthIncomeTbl on ccs.id = thisMonthIncomeTbl.customer_service_id
            set ccs.this_month_income = thisMonthIncomeTbl.total_amount,
                ccs.this_month_income_rpa_result = thisMonthIncomeTbl.rpa_result
        where ccs.is_del = 0 and ccs.service_status = 1
        <if test="customerServiceId != null">
            and ccs.id = #{customerServiceId}
        </if>
    </update>
    <update id="updateCustomerServiceThisSeasonIncome">
        update c_customer_service ccs
        join (select customer_service_id, ifnull(sum(all_ticket_amount + no_ticket_income_amount), 0) as total_amount,max(rpa_result) as rpa_result
        from c_customer_service_period_month_income
        where period &gt;= #{thisSeasonStart} and period &lt;= #{thisSeasonEnd}
        group by customer_service_id) thisSeasonIncomeTbl on ccs.id = thisSeasonIncomeTbl.customer_service_id

        set
        ccs.this_season_income = thisSeasonIncomeTbl.total_amount,
        ccs.this_season_income_rpa_result = thisSeasonIncomeTbl.rpa_result

        where ccs.is_del = 0 and ccs.service_status = 1
        <if test="customerServiceId != null">
            and ccs.id = #{customerServiceId}
        </if>
    </update>
    <update id="updateCustomerServiceThisYearIncome">
        update c_customer_service ccs
        join (select customer_service_id, ifnull(sum(all_ticket_amount + no_ticket_income_amount), 0) as total_amount,max(rpa_result) as rpa_result
        from c_customer_service_period_month_income
        where period &gt;= #{thisYearStart} and period &lt;= #{thisYearEnd}
        group by customer_service_id) thisYearIncomeTbl on ccs.id = thisYearIncomeTbl.customer_service_id
        set
        ccs.this_year_income = thisYearIncomeTbl.total_amount,
        ccs.this_year_income_rpa_result = thisYearIncomeTbl.rpa_result
        where ccs.is_del = 0 and ccs.service_status = 1
        <if test="customerServiceId != null">
            and ccs.id = #{customerServiceId}
        </if>
    </update>
    <update id="updateCustomerServiceThis12MonthIncome">
        update c_customer_service ccs
        join (select customer_service_id, ifnull(sum(all_ticket_amount + no_ticket_income_amount), 0) as total_amount,max(rpa_result) as rpa_result
        from c_customer_service_period_month_income
        where period &gt;= #{this12MonthStart} and period &lt;= #{this12MonthEnd}
        group by customer_service_id) this12MonthIncomeTbl on ccs.id = this12MonthIncomeTbl.customer_service_id

        set
        ccs.this_12_month_income = this12MonthIncomeTbl.total_amount,
        ccs.this_12_month_income_rpa_result = this12MonthIncomeTbl.rpa_result
        where ccs.is_del = 0 and ccs.service_status = 1
        <if test="customerServiceId != null">
            and ccs.id = #{customerServiceId}
        </if>
    </update>
    <update id="updateCustomerServiceTicketTime">
        update c_customer_service ccs
        join (select customer_service_id,
        max(ticket_time) as ticketTime
        from c_customer_service_period_month_income where period &gt;= #{this12MonthStart} and period &lt;= #{thisYearEnd} group by customer_service_id) a
        set
        ccs.ticket_time = a.ticketTime
        where ccs.is_del = 0 and ccs.service_status = 1
        <if test="customerServiceId != null">
            and ccs.id = #{customerServiceId}
        </if>
    </update>
    <update id="updateSettlementStatusBySettlementOrderDatas">
        update c_customer_service set settlement_status = 3
        where id in (select distinct business_id from c_settlement_order_data where settlement_order_id = #{settlementOrderId})
    </update>
    <update id="updateLastInAccountId">
        update
        c_customer_service cs left JOIN
        (SELECT cca.* from c_customer_service_cashier_accounting cca
        JOIN (
        SELECT
        cca2.customer_service_id,
        MAX(cca2.period) AS maxPeriod
        FROM c_customer_service_cashier_accounting cca2
        WHERE
        cca2.is_del = 0
        AND (
        cca2.major_income_total IS NOT NULL OR
        cca2.major_cost_total IS NOT NULL OR
        cca2.profit_total IS NOT NULL OR
        cca2.prior_year_expense_increase IS NOT NULL OR
        cca2.tax_report_count IS NOT NULL OR
        cca2.tax_report_salary_total IS NOT NULL
        )
        AND cca2.period &gt;= #{startPeriod} AND cca2.period &lt;= #{endPeriod} AND cca2.`type` = 1
        GROUP BY cca2.customer_service_id
        ) AS max_periods
        ON cca.customer_service_id = max_periods.customer_service_id
        AND cca.period = max_periods.maxPeriod AND cca.`type` = 1 AND cca.is_del = 0) a
        ON cs.id = a.customer_service_id
        SET cs.last_in_account_id = a.id
        WHERE cs.is_del = 0
    </update>
    <update id="removeCustomerServicePeriodMonthTaxTypeCheckBlank">
        UPDATE c_customer_service_period_month_tax_type_check
        SET tax_type = REPLACE(REPLACE(REPLACE(tax_type, ' ', ''), '\n', ''), '\r', '')
        WHERE tax_type LIKE '% %' OR tax_type LIKE '%\n%' OR tax_type LIKE '%\r%'
    </update>
    <update id="removeCustomerServiceBankAccountBlank">
        UPDATE c_customer_service_bank_account
        SET bank_account_number = REPLACE(REPLACE(REPLACE(bank_account_number, ' ', ''), '\n', ''), '\r', ''),
            bank_name = REPLACE(REPLACE(REPLACE(bank_name, ' ', ''), '\n', ''), '\r', '')
        WHERE bank_account_number LIKE '% %' OR bank_account_number LIKE '%\n%' OR bank_account_number LIKE '%\r%'
           OR bank_name LIKE '% %' OR bank_name LIKE '%\n%' OR bank_name LIKE '%\r%'
    </update>
    <update id="removeCustomerServicePeriodMonthBlank">
        UPDATE c_customer_service_period_month
        SET customer_name = REPLACE(REPLACE(REPLACE(customer_name, ' ', ''), '\n', ''), '\r', ''),
            credit_code = REPLACE(REPLACE(REPLACE(credit_code, ' ', ''), '\n', ''), '\r', ''),
            tax_number = REPLACE(REPLACE(REPLACE(tax_number, ' ', ''), '\n', ''), '\r', '')
        WHERE customer_name LIKE '% %' OR customer_name LIKE '%\n%' OR customer_name LIKE '%\r%'
           OR credit_code LIKE '% %' OR credit_code LIKE '%\n%' OR credit_code LIKE '%\r%'
           OR tax_number LIKE '% %' OR tax_number LIKE '%\n%' OR tax_number LIKE '%\r%'
    </update>
    <update id="removeCustomerServiceBlank">
        UPDATE c_customer_service
        SET customer_name = REPLACE(REPLACE(REPLACE(customer_name, ' ', ''), '\n', ''), '\r', ''),
            customer_company_name = REPLACE(REPLACE(REPLACE(customer_company_name, ' ', ''), '\n', ''), '\r', ''),
            credit_code = REPLACE(REPLACE(REPLACE(credit_code, ' ', ''), '\n', ''), '\r', ''),
            tax_number = REPLACE(REPLACE(REPLACE(tax_number, ' ', ''), '\n', ''), '\r', ''),
            service_number = REPLACE(REPLACE(REPLACE(service_number, ' ', ''), '\n', ''), '\r', '')
        WHERE customer_name LIKE '% %' OR customer_name LIKE '%\n%' OR customer_name LIKE '%\r%'
           OR customer_company_name LIKE '% %' OR customer_company_name LIKE '%\n%' OR customer_company_name LIKE '%\r%'
           OR credit_code LIKE '% %' OR credit_code LIKE '%\n%' OR credit_code LIKE '%\r%'
           OR tax_number LIKE '% %' OR tax_number LIKE '%\n%' OR tax_number LIKE '%\r%'
           OR service_number LIKE '% %' OR service_number LIKE '%\n%' OR service_number LIKE '%\r%'
    </update>
    <update id="removeNewCustomerBankAccountBlank">
        UPDATE c_new_customer_bank_account
        SET bank_account_number = REPLACE(REPLACE(REPLACE(bank_account_number, ' ', ''), '\n', ''), '\r', ''),
            bank_name = REPLACE(REPLACE(REPLACE(bank_name, ' ', ''), '\n', ''), '\r', '')
        WHERE bank_account_number LIKE '% %' OR bank_account_number LIKE '%\n%' OR bank_account_number LIKE '%\r%'
           OR bank_name LIKE '% %' OR bank_name LIKE '%\n%' OR bank_name LIKE '%\r%'
    </update>
    <update id="removeNewCustomerTaxTypeCheckBlank">
        UPDATE c_new_customer_tax_type_check
        SET tax_type = REPLACE(REPLACE(REPLACE(tax_type, ' ', ''), '\n', ''), '\r', '')
        WHERE tax_type LIKE '% %' OR tax_type LIKE '%\n%' OR tax_type LIKE '%\r%'
    </update>
    <update id="removeNewCustomerInfoBlank">
        UPDATE c_new_customer_info
        SET customer_name = REPLACE(REPLACE(REPLACE(customer_name, ' ', ''), '\n', ''), '\r', ''),
            credit_code = REPLACE(REPLACE(REPLACE(credit_code, ' ', ''), '\n', ''), '\r', ''),
            tax_number = REPLACE(REPLACE(REPLACE(tax_number, ' ', ''), '\n', ''), '\r', '')
        WHERE customer_name LIKE '% %' OR customer_name LIKE '%\n%' OR customer_name LIKE '%\r%'
           OR credit_code LIKE '% %' OR credit_code LIKE '%\n%' OR credit_code LIKE '%\r%'
           OR tax_number LIKE '% %' OR tax_number LIKE '%\n%' OR tax_number LIKE '%\r%'
    </update>
    <update id="removeSysUserBlank">
        UPDATE sys_user
        SET user_name = REPLACE(REPLACE(REPLACE(user_name, ' ', ''), '\n', ''), '\r', ''),
            nick_name = REPLACE(REPLACE(REPLACE(nick_name, ' ', ''), '\n', ''), '\r', ''),
            phonenumber = REPLACE(REPLACE(REPLACE(phonenumber, ' ', ''), '\n', ''), '\r', '')
        WHERE user_name LIKE '% %' OR user_name LIKE '%\n%' OR user_name LIKE '%\r%'
           OR nick_name LIKE '% %' OR nick_name LIKE '%\n%' OR nick_name LIKE '%\r%'
           OR phonenumber LIKE '% %' OR phonenumber LIKE '%\n%' OR phonenumber LIKE '%\r%'
    </update>

    <delete id="deleteCCustomerServiceById" parameterType="Long">
        delete from c_customer_service where id = #{id}
    </delete>

    <delete id="deleteCCustomerServiceByIds" parameterType="String">
        delete from c_customer_service where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="updateFileWaitDealList" resultType="com.bxm.customer.domain.dto.CustomerDeliverMiniDTO">
        select
        distinct
        ccs.id as customerServiceId,
        ccs.customer_name as customerName,
        ccs.credit_code as creditCode,
        ccspm.period,ccspm.id
        from c_customer_service_period_month ccspm
        left join c_customer_deliver ccd on ccspm.id = ccd.customer_service_period_month_id and ccd.is_del = 0 and ccd.deliver_type = #{deliverType}
        left join c_customer_service ccs on ccspm.customer_service_id = ccs.id and ccs.is_del = 0
        <where>
            <if test="operType != 1">
                and
                ccd.has_changed = 0
                <if test="deliverStatusList != null and deliverStatusList.size > 0">
                    and ccd1.status in
                    <foreach collection="deliverStatusList" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </if>
            </if>
            <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                <if test="userDept.deptType != null and userDept.deptType == 1">
                    and (ccspm.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType != null and userDept.deptType == 2">
                    and (ccspm.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="operType != null and operType == 1">
                and ((ccd1.id IS NULL AND ccspm.id in (select business_id from c_business_tag_relation where tag_id = 2 and business_type = 2)) OR (ccd2.id IS NULL AND ccspm.id in (select business_id from c_business_tag_relation where tag_id = 3 and business_type = 2)))
                and ccspm.service_type = 1
            </if>
            <if test="period != null">
                and ccspm.period = #{period}
            </if>
        </where>
        order by ccspm.period desc,ccspm.id desc
    </select>

    <select id="customerDeliverMiniList" resultType="com.bxm.customer.domain.dto.CustomerDeliverMiniDTO">
        select
        ccspm.customer_service_id as customerServiceId,
        ccspm.id as customerServicePeriodMonthId,
        <if test="deliverStatus != null and deliverStatus == 8">
            ccd1.id as deliverId,
        </if>
        <if test="deliverStatus != null and deliverStatus != 8">
            ccd.id as deliverId,
        </if>
        ccspm.period as period,
        ccs.customer_name as customerName,
        ccs.customer_company_name as customerCompanyName,
        ccs.credit_code as creditCode,
        ccspm.business_dept_id as businessDeptId,
        ccspm.advisor_dept_id as advisorDeptId,
--         ccs.advisor_dept_id as advisorDeptId,
        sd1.dept_name as advisorDeptName,
        ccspm.accounting_dept_id as accountingDeptId,
--         ccs.accounting_dept_id as accountingDeptId,
        sd2.dept_name as accountingDeptName,
        ccd.report_amount as reportAmount,
        ccd.total_tax_amount as totalAmount,
        ccd.has_changed as hasChanged,
        ccd.title as title,
        ccd.deliver_type as deliverType,
        ccd.tax_check_type as taxCheckType,
        ccd.last_oper_type as lastOperType,
        ccd.last_oper_time as lastOperTime,
        ccd.last_oper_name as lastOperName,
        ccd.last_oper_remark as lastOperRemark,
        <if test="deliverStatus != null and deliverStatus == 8">
            if(ccd1.id is null, -1, ccd1.status) as deliverStatus
        </if>
        <if test="deliverStatus != null and deliverStatus != 8">
            if(ccd.id is null, -1, ccd.status) as deliverStatus
        </if>
        from c_customer_service_period_month ccspm
        left join c_customer_deliver ccd on ccspm.id = ccd.customer_service_period_month_id and ccd.is_del = 0 and ccd.deliver_type = #{deliverType}
        <if test="deliverStatus != null and deliverStatus == 8"> and ccd.`status` in (5, 104)</if>
        <if test="deliverStatus != null and deliverStatus == 8">
            left join c_customer_deliver ccd1 on ccspm.id = ccd1.customer_service_period_month_id and ccd1.is_del = 0 and ccd1.deliver_type = #{deliverType}
        </if>
        left join c_customer_service ccs on ccspm.customer_service_id = ccs.id and ccs.is_del = 0
        left join sys_dept sd1 on ccspm.advisor_dept_id = sd1.dept_id and sd1.del_flag = '0'
        left join sys_dept sd2 on ccspm.accounting_dept_id = sd2.dept_id and sd2.del_flag = '0'
        <where>
            <if test="taxCheckType != null and taxCheckType != ''">
                and ccd.tax_check_type like concat('%',#{taxCheckType},'%')
            </if>
            <if test="deliverStatus == 16">
                and ccd.`status` in (2,6,7,9,11)
            </if>
            <if test="deliverStatus == 17">
                and ccd.`status` in (0,1,3,4)
            </if>
            <if test="status != null">
                <if test="status == 21">
                    and ccd.`status` in (6,11,105)
                </if>
                <if test="status != 21">
                    and ccd.`status` = #{status}
                </if>
            </if>
            <if test="deliverStatus == 13">
                and ccd.has_changed = 1
            </if>
            <if test="deliverStatus == 8">
                and (ccd1.id is null or ccd1.has_changed = 0)
            </if>
            <if test="deliverStatus != -1 and deliverStatus != -2 and deliverStatus != 8 and deliverStatus != 13 and deliverStatus != 16 and deliverStatus != 17">
                and ccd.has_changed = 0
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                <if test="queryDeptType != null and queryDeptType == 1">
                    and (ccspm.advisor_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="queryDeptType != null and queryDeptType == 2">
                    and (ccspm.accounting_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="queryDeptType == null">
                    and (ccspm.advisor_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="advisorDeptId != null">
                and ccspm.advisor_dept_id = #{advisorDeptId}
            </if>
            <if test="accountingDeptId != null">
                and ccspm.accounting_dept_id = #{accountingDeptId}
            </if>
            <if test="customerServiceAdvisorDeptId != null">
                and ccs.advisor_dept_id = #{customerServiceAdvisorDeptId}
            </if>
            <if test="customerServiceAccountingDeptId != null">
                and ccs.accounting_dept_id = #{customerServiceAccountingDeptId}
            </if>
            <if test="periodAdvisorDeptId != null">
                and ccspm.advisor_dept_id = #{periodAdvisorDeptId}
            </if>
            <if test="periodAccountingDeptId != null">
                and ccspm.accounting_dept_id = #{periodAccountingDeptId}
            </if>
            <if test="customerServiceTaxType != null">
                and ccs.tax_type = #{customerServiceTaxType}
            </if>
            <if test="periodTaxType != null">
                and ccspm.tax_type = #{periodTaxType}
            </if>
            <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                <if test="userDept.deptType != null and userDept.deptType == 1">
                    and (ccspm.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType != null and userDept.deptType == 2">
                    and (ccspm.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="deliverStatus != null and deliverStatus != -1 and deliverStatus != 8">
                <if test="deliverStatusList != null and deliverStatusList.size > 0">
                    and ccd.status in
                    <foreach collection="deliverStatusList" item="deliverStatus" open="(" separator="," close=")">
                        #{deliverStatus}
                    </foreach>
                </if>
            </if>
            <if test="deliverStatus != null and deliverStatus == -1">
                <if test="deliverType != 7 and deliverType != 8 and deliverType != 9">
                    and ccd.id is null and ccspm.service_type = 1
                </if>
                <if test="deliverType == 7 or deliverType == 8 or deliverType == 9">
                    and ccd.id is null
                </if>
            </if>
            <if test="deliverStatus != null and deliverStatus == -2">
                <if test="deliverType != 7 and deliverType != 8 and deliverType != 9">
                    and ccspm.service_type = 1
                </if>
            </if>
            <if test="deliverStatus != null and deliverStatus == 8">
                and ccd.id is null
            </if>
            <if test="customerName != null and customerName != ''">
                and (ccs.customer_name like concat('%', #{customerName}, '%') or ccs.customer_company_name like concat('%', #{customerName}, '%') or ccs.credit_code like concat('%', #{customerName}, '%'))
            </if>
            <if test="customerServiceTagName != null and customerServiceTagName != ''">
                <if test="customerServiceTagIncludeFlag != null and customerServiceTagIncludeFlag == 1 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccs.id in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="customerServiceTagIncludeFlag != null and customerServiceTagIncludeFlag == 0 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccs.id not in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="periodTagName != null and periodTagName != ''">
                <if test="periodTagIncludeFlag != null and periodTagIncludeFlag == 1 and periodIds != null and periodIds.size > 0">
                    and ccspm.id in
                    <foreach collection="periodIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="periodTagIncludeFlag != null and periodTagIncludeFlag == 0 and periodIds != null and periodIds.size > 0">
                    and ccspm.id not in
                    <foreach collection="periodIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="period != null">
                <if test="deliverStatus == 16">
                    and ccspm.period &lt; #{period}
                </if>
                <if test="deliverStatus == 17">
                    and ccspm.period &lt; #{period}
                </if>
                <if test="deliverStatus != 16 and deliverStatus != 17">
                    and ccspm.period = #{period}
                </if>
            </if>
<!--            <if test="deliverStatus != null and deliverStatus == 8">-->
<!--                and ccspm.service_status = 3-->
<!--            </if>-->
<!--            <if test="deliverStatus != null and deliverStatus != 8 and deliverStatus != 13">-->
<!--                and ccspm.service_status != 3-->
<!--            </if>-->
            <if test="deliverStatus == -1 or deliverStatus == 8 or deliverStatus == -2">
                <if test="deliverType != null and deliverType == 1 and deliverStatus != null">
                    and ccspm.id in (select business_id from c_business_tag_relation where tag_id = 2 and business_type = 2)
                </if>
                <if test="deliverType != null and deliverType == 2 and deliverStatus != null">
                    and ccspm.id in (select business_id from c_business_tag_relation where tag_id = 3 and business_type = 2)
                </if>
                <if test="deliverType != null and deliverType == 3 and deliverStatus != null">
                    <if test="month != 3 and month != 6 and month != 9 and month != 12">
                        and ccspm.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type = 1)
                    </if>
                    <if test="month == 3 or month == 6 or month == 9 or month == 12">
                        and ccspm.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type in (1, 2))
                    </if>
                </if>
                <if test="deliverType != null and deliverType == 6 and deliverStatus != null">
                    <if test="month == 1 or month == 2 or month == 4 or month == 5 or month == 7 or month == 8 or month == 10 or month == 11">
                        and exists (select 1 from c_customer_service_period_month_tax_type_check where customer_service_period_month_id = ccspm.id and tax_type like '个人所得税%' and tax_type != '个人所得税' and report_type = 1)
                    </if>
                    <if test="month == 3 or month == 9">
                        and exists (select 1 from c_customer_service_period_month_tax_type_check where customer_service_period_month_id = ccspm.id and tax_type like '个人所得税%' and tax_type != '个人所得税' and report_type in (1, 2))
                    </if>
                    <if test="month == 6">
                        and exists (select 1 from c_customer_service_period_month_tax_type_check where customer_service_period_month_id = ccspm.id and tax_type like '个人所得税%' and tax_type != '个人所得税' and report_type in (1, 2, 5))
                    </if>
                    <if test="month == 12">
                        and exists (select 1 from c_customer_service_period_month_tax_type_check where customer_service_period_month_id = ccspm.id and tax_type like '个人所得税%' and tax_type != '个人所得税' and report_type in (1, 2, 3, 5))
                    </if>
                </if>
                <if test="deliverType != null and deliverType == 4 and deliverStatus != null">
                    <if test="month != 3 and month != 6 and month != 9 and month != 12">
                        and exists (select 1 from c_customer_service_period_month_tax_type_check where customer_service_period_month_id = ccspm.id and tax_type not like '个人所得税%' and report_type = 1)
                    </if>
                </if>
                <if test="deliverType != null and deliverType == 5 and deliverStatus != null">
                    and ccspm.tax_type = 2
                </if>
                <if test="deliverType != null and deliverType == 9 and deliverStatus != null">
                    AND exists (
                        SELECT 1
                        FROM c_customer_service_period_month_tax_type_check pt
                        JOIN c_customer_service_period_month spm2
                        ON pt.customer_service_period_month_id = spm2.id AND pt.tax_type like '残疾人就业保障金%'
                        WHERE ccspm.customer_service_id = spm2.customer_service_id AND ccspm.`year` = spm2.`year`
                    )
                </if>
            </if>
        </where>
        <if test="deliverStatus != null and deliverStatus == 16">
            order by ccd.period desc,ccd.id desc
        </if>
        <if test="deliverStatus != null and deliverStatus == 17">
            order by ccd.period desc,ccd.id desc
        </if>
        <if test="deliverStatus != null and deliverStatus != 16 and deliverStatus != 17">
            order by ccspm.period desc,ccspm.id desc
        </if>
    </select>
    <select id="waitCreateDeliverList" resultType="com.bxm.customer.domain.dto.CustomerDeliverMiniDTO">
        select
        ccspm.customer_service_id as customerServiceId,
        ccspm.id as customerServicePeriodMonthId,
        null as deliverId,
        ccspm.period as period,
        ccspm.advisor_dept_id as advisorDeptId,
        ccspm.accounting_dept_id as accountingDeptId,
        -1 as deliverStatus
        from c_customer_service_period_month ccspm
        left join c_customer_deliver ccd on ccspm.id = ccd.customer_service_period_month_id and ccd.is_del = 0 and ccd.deliver_type = #{deliverType}
        <where>
            ccd.id is null and ccspm.service_status != 3 and ccspm.service_type = 1 and ccspm.business_top_dept_id in (59, 266)
            <if test="period != null">
                and ccspm.period = #{period}
            </if>
            <if test="deliverType != null and deliverType == 1">
                and ccspm.id in (select business_id from c_business_tag_relation where tag_id = 2 and business_type = 2)
            </if>
            <if test="deliverType != null and deliverType == 2">
                and ccspm.id in (select business_id from c_business_tag_relation where tag_id = 3 and business_type = 2)
            </if>
            <if test="deliverType != null and deliverType == 3">
                <if test="month != 3 and month != 6 and month != 9 and month != 12">
                    and ccspm.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type = 1)
                </if>
                <if test="month == 3 or month == 6 or month == 9 or month == 12">
                    and ccspm.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type in (1, 2))
                </if>
                and ccspm.id not in (select business_id from c_business_tag_relation where tag_id = 5 and business_type = 2)
            </if>
            <if test="deliverType != null and deliverType == 4">
                <if test="month != 3 and month != 6 and month != 9 and month != 12">
                    and ccspm.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type not like '个人所得税%' and report_type = 1)
                    and
                    (
                    (ccspm.id not in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '增值税')) or
                    (ccspm.id not in (select business_id from c_business_tag_relation where tag_id = 9 and business_type = 2)) or
                    (ccspm.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '增值税' and report_type = 2) and ccspm.id in (select business_id from c_business_tag_relation where tag_id = 9 and business_type = 2))
                    )
                </if>
                <if test="month == 3 or month == 6 or month == 9 or month == 12">
                    and
                    (
                    (ccspm.id not in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '增值税')) or
                    (ccspm.id not in (select business_id from c_business_tag_relation where tag_id = 9 and business_type = 2))
                    )
                </if>
                and not exists (select 1 from c_business_tag_relation where business_id = ccspm.id and business_type = 2 and tag_id = 252)
            </if>
            <if test="deliverType != null and deliverType == 6">
                <if test="month == 1 or month == 2 or month == 4 or month == 5 or month == 7 or month == 8 or month == 10 or month == 11">
                    and exists (select 1 from c_customer_service_period_month_tax_type_check where customer_service_period_month_id = ccspm.id and tax_type like '个人所得税%' and tax_type != '个人所得税' and report_type = 1)
                </if>
                <if test="month == 3 or month == 9">
                    and exists (select 1 from c_customer_service_period_month_tax_type_check where customer_service_period_month_id = ccspm.id and tax_type like '个人所得税%' and tax_type != '个人所得税' and report_type in (1, 2))
                </if>
                <if test="month == 6">
                    and exists (select 1 from c_customer_service_period_month_tax_type_check where customer_service_period_month_id = ccspm.id and tax_type like '个人所得税%' and tax_type != '个人所得税' and report_type in (1, 2, 5))
                </if>
                <if test="month == 12">
                    and exists (select 1 from c_customer_service_period_month_tax_type_check where customer_service_period_month_id = ccspm.id and tax_type like '个人所得税%' and tax_type != '个人所得税' and report_type in (1, 2, 3, 5))
                </if>
            </if>
        </where>
        order by ccspm.id desc
    </select>
    <select id="customerServicePeriodYearList"
            resultType="com.bxm.customer.domain.dto.CustomerServicePeriodYearDTO">
        select
            ccspy.id as id,
            ccspy.customer_service_id as customerServiceId,
            ccs.customer_name as customerName,
            ccs.customer_company_name as customerCompanyName,
            ccs.service_number as serviceNumber,
            ccs.credit_code as creditCode,
            ccs.tax_type as taxType,
            ccs.service_status as serviceStatus,
            sd.dept_name as businessDeptName,
            ccs.business_dept_id as businessDeptId,
            ccs.advisor_dept_id as advisorDeptId,
            sd1.dept_name as advisorDeptName,
            ccs.accounting_dept_id as accountingDeptId,
            sd2.dept_name as accountingDeptName,
            ccs.first_account_period as startPeriod,
            ccs.end_account_period as endPeriod,
            ccspy.period as period,
            ccspy.last_year_deductible as lastYearDeductible,
            ccspy.prior_year_estimation_not_reversed as priorYearEstimationNotReversed,
            ccspy.prior_year_depreciation_adjustment as priorYearDepreciationAdjustment,
            ccspy.full_year_closing as fullYearClosing,
            cca.profit_get_time as profitGetTime,
            cca.period as lastInAccountPeriod,
            cca.major_income_total as mainIncome,
            cca.major_cost_total as mainCost,
            cca.profit_total as profit,
            cca.prior_year_expense_increase as priorYearExpenseIncrease,
            cca.tax_report_count as taxReportCount,
            cca.tax_report_salary_total as taxReportSalaryTotal,
            cca.profit as profit1,
            cca.tempesti as tempesti,
            cca.welfare as welfare,
            cca.enterain as enterain
        from c_customer_service_period_year ccspy join c_customer_service ccs on ccspy.customer_service_id = ccs.id and ccs.is_del = 0
            left join c_customer_service_cashier_accounting cca on ccspy.last_in_account_id = cca.id and cca.is_del = 0
                                                  left join sys_dept sd on ccs.business_dept_id = sd.dept_id and sd.del_flag = '0'
                                                  left join sys_dept sd1 on ccs.advisor_dept_id = sd1.dept_id and sd1.del_flag = '0'
                                                  left join sys_dept sd2 on ccs.accounting_dept_id = sd2.dept_id and sd2.del_flag = '0'
        <where>
            <if test="userDept.isAdmin == false and userDept.deptIds != null and userDept.deptIds.size > 0">
                <if test="userDept.deptType == 1">
                    and (ccs.advisor_dept_id in
                    <foreach collection="userDept.deptIds" separator="," item="deptId" close=")" open="(">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" separator="," item="deptId" close=")" open="(">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2">
                    and (ccs.accounting_dept_id in
                    <foreach collection="userDept.deptIds" separator="," item="deptId" close=")" open="(">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" separator="," item="deptId" close=")" open="(">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.annualSettlement != null">
                <if test="vo.annualSettlement == 0">
                    and not exists (
                        select 1 from c_customer_service_period_month spm where
                        spm.customer_service_id = ccspy.customer_service_id and ccspy.period_last_month = spm.period
                    )
                </if>
                <if test="vo.annualSettlement == 1">
                    and exists (
                    select 1 from c_customer_service_period_month spm where
                    spm.customer_service_id = ccspy.customer_service_id and ccspy.period_last_month = spm.period
                    )
                </if>
            </if>
            <if test="vo.lastPeriod != null">
                and cca.period = #{vo.lastPeriod}
            </if>
            <if test="vo.profitGetTimeStart != null and vo.profitGetTimeStart != ''">
                and cca.profit_get_time &gt;= #{vo.profitGetTimeStart}
            </if>
            <if test="vo.profitGetTimeEnd != null and vo.profitGetTimeEnd != ''">
                and cca.profit_get_time &lt;= #{vo.profitGetTimeEnd}
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (ccs.customer_name like concat('%',#{vo.keyWord},'%') or ccs.customer_company_name like concat('%',#{vo.keyWord},'%') or ccs.credit_code like concat('%',#{vo.keyWord},'%'))
            </if>
            <if test="vo.serviceNumber != null and vo.serviceNumber != ''">
                and ccs.service_number like concat('%',#{vo.serviceNumber},'%')
            </if>
            <if test="vo.tagName != null and vo.tagName != ''">
                <if test="vo.tagIncludeFlag != null and vo.tagIncludeFlag == 1 and vo.customerServiceIds != null and vo.customerServiceIds.size > 0">
                    and ccs.id in
                    <foreach collection="vo.customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="vo.tagIncludeFlag != null and vo.tagIncludeFlag == 0 and vo.customerServiceIds != null and vo.customerServiceIds.size > 0">
                    and ccs.id not in
                    <foreach collection="vo.customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="vo.serviceStatus != null">
                and ccs.service_status = #{vo.serviceStatus}
            </if>
            <if test="vo.taxType != null">
                and ccs.tax_type = #{vo.taxType}
            </if>
            <if test="vo.businessDeptId != null">
                and ccs.business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.businessDeptIdList != null and vo.businessDeptIdList != ''">
                and ccs.business_dept_id in (${vo.businessDeptIdList})
            </if>
            <if test="vo.accountingTopDeptIdList != null and vo.accountingTopDeptIdList != ''">
                and ccs.accounting_top_dept_id in (${vo.accountingTopDeptIdList})
            </if>
            <if test="vo.advisorDeptId != null">
                and ccs.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and ccs.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.yearMin != null and vo.yearMin != ''">
                and ccspy.period &gt;= #{vo.yearMin}
            </if>
            <if test="vo.yearMax != null and vo.yearMax != ''">
                and ccspy.period &lt;= #{vo.yearMax}
            </if>
            <if test="vo.year != null and vo.year != ''">
                and ccspy.period = #{vo.year}
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                and ccs.id in
                <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="vo.fullYearClosing != null">
                and ccspy.full_year_closing = #{vo.fullYearClosing}
            </if>
        </where>
        order by ccspy.period desc, ccs.id desc
    </select>
    <select id="medicalSocialWaitDealList" resultType="com.bxm.customer.domain.dto.CustomerDeliverMiniDTO">
        select
            distinct
            ccs.id as customerServiceId,
            ccs.customer_name as customerName,
            ccs.credit_code as creditCode,
            ccspm.period,ccspm.id
        from c_customer_service_period_month ccspm
        left join c_customer_deliver ccd1 on ccspm.id = ccd1.customer_service_period_month_id and ccd1.is_del = 0 and ccd1.deliver_type = 1
        left join c_customer_deliver ccd2 on ccspm.id = ccd2.customer_service_period_month_id and ccd2.is_del = 0 and ccd2.deliver_type = 2
        left join c_customer_service ccs on ccspm.customer_service_id = ccs.id and ccs.is_del = 0
        <where>
            <if test="operType != 1">
                and
                ((ccd1.has_changed = 0
                <if test="deliverStatusList != null and deliverStatusList.size > 0">
                    and ccd1.status in
                    <foreach collection="deliverStatusList" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </if>)
                or (ccd2.has_changed = 0
                <if test="deliverStatusList != null and deliverStatusList.size > 0">
                    and ccd2.status in
                    <foreach collection="deliverStatusList" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </if>)
                )
            </if>
            <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                <if test="userDept.deptType != null and userDept.deptType == 1">
                    and (ccspm.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType != null and userDept.deptType == 2">
                    and (ccspm.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="operType != null and operType == 1">
                and ((ccd1.id IS NULL AND ccspm.id in (select business_id from c_business_tag_relation where tag_id = 2 and business_type = 2)) OR (ccd2.id IS NULL AND ccspm.id in (select business_id from c_business_tag_relation where tag_id = 3 and business_type = 2)))
                and ccspm.service_type = 1
            </if>
            <if test="period != null">
                and ccspm.period = #{period}
            </if>
        </where>
        order by ccspm.period desc,ccspm.id desc
    </select>
    <select id="customerServiceAccountingDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        ccs.accounting_dept_id as deptId,
        count(1) as dataCount
        from c_customer_service ccs
        <where>
            ccs.is_del = 0 and ccs.accounting_dept_id is not null
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (ccs.customer_name like concat('%', #{vo.keyWord}, '%') or ccs.customer_company_name like concat('%', #{vo.keyWord}, '%')
                or ccs.credit_code like concat('%', #{vo.keyWord}, '%'))
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and ccs.customer_name like concat('%', #{vo.customerName}, '%')
            </if>
            <if test="vo.customerCompanyName != null and vo.customerCompanyName != ''">
                and ccs.customer_company_name like concat('%', #{vo.customerCompanyName}, '%')
            </if>
            <if test="vo.serviceNumber != null and vo.serviceNumber != ''">
                and ccs.service_number like concat('%', #{vo.serviceNumber}, '%')
            </if>
            <if test="vo.taxType != null">
                and ccs.tax_type = #{vo.taxType}
            </if>
            <if test="vo.serviceStatus != null">
                and ccs.service_status = #{vo.serviceStatus}
            </if>
            <if test="vo.ticketTimeStart != null and vo.ticketTimeStart != ''">
                and ccs.ticket_time &gt;= #{vo.ticketTimeStart}
            </if>
            <if test="vo.ticketTimeEnd != null and vo.ticketTimeEnd != ''">
                and ccs.ticket_time &lt;= #{vo.ticketTimeEnd}
            </if>
            <if test="isAdmin != null and isAdmin == 0">
                <if test="deptType != null and deptType == 1 and deptIds != null and deptIds.size > 0">
                    and (ccs.advisor_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="deptType != null and deptType == 2 and deptIds != null and deptIds.size > 0">
                    and (ccs.accounting_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.queryDeptIds != null and vo.queryDeptIds.size > 0">
                <if test="deptType != null and deptType == 1">
                    and (ccs.advisor_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="deptType != null and deptType == 2">
                    and (ccs.accounting_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.advisorSearchDeptIds != null and vo.advisorSearchDeptIds.size > 0">
                and (ccs.advisor_dept_id in
                <foreach collection="vo.advisorSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or ccs.advisor_top_dept_id in
                <foreach collection="vo.advisorSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>)
            </if>
            <if test="vo.accountingSearchDeptIds != null and vo.accountingSearchDeptIds.size > 0">
                and (ccs.accounting_dept_id in
                <foreach collection="vo.accountingSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or ccs.accounting_top_dept_id in
                <foreach collection="vo.accountingSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>)
            </if>
            <if test="vo.tagName != null and vo.tagName != ''">
                <if test="tagIncludeFlag != null and tagIncludeFlag == 1 and ids != null and ids.size > 0">
                    and ccs.id in
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="tagIncludeFlag != null and tagIncludeFlag == 0 and ids != null and ids.size > 0">
                    and ccs.id not in
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="vo.businessDeptId != null">
                and ccs.business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.accountingTopDeptId != null">
                and ccs.accounting_top_dept_id = #{vo.accountingTopDeptId}
            </if>
            <if test="vo.startPeriodStart != null">
                and ccs.first_account_period >= #{vo.startPeriodStart}
            </if>
            <if test="vo.startPeriodEnd != null">
                and ccs.first_account_period &lt;= #{vo.startPeriodEnd}
            </if>
            <if test="vo.endPeriodStart != null">
                and ccs.end_account_period >= #{vo.endPeriodStart}
            </if>
            <if test="vo.endPeriodEnd != null">
                and ccs.end_account_period &lt;= #{vo.endPeriodEnd}
            </if>
            <if test="vo.advisorDeptId != null">
                and ccs.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and ccs.accounting_dept_id = #{vo.accountingDeptId}
            </if>
        </where>
        group by ccs.accounting_dept_id
    </select>
    <select id="customerServiceAdvisorDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        ccs.advisor_dept_id as deptId,
        count(1) as dataCount
        from c_customer_service ccs
        <where>
            ccs.is_del = 0 and ccs.advisor_dept_id is not null
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (ccs.customer_name like concat('%', #{vo.keyWord}, '%') or ccs.customer_company_name like concat('%', #{vo.keyWord}, '%')
                or ccs.credit_code like concat('%', #{vo.keyWord}, '%'))
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and ccs.customer_name like concat('%', #{vo.customerName}, '%')
            </if>
            <if test="vo.customerCompanyName != null and vo.customerCompanyName != ''">
                and ccs.customer_company_name like concat('%', #{vo.customerCompanyName}, '%')
            </if>
            <if test="vo.serviceNumber != null and vo.serviceNumber != ''">
                and ccs.service_number like concat('%', #{vo.serviceNumber}, '%')
            </if>
            <if test="vo.taxType != null">
                and ccs.tax_type = #{vo.taxType}
            </if>
            <if test="vo.serviceStatus != null">
                and ccs.service_status = #{vo.serviceStatus}
            </if>
            <if test="vo.ticketTimeStart != null and vo.ticketTimeStart != ''">
                and ccs.ticket_time &gt;= #{vo.ticketTimeStart}
            </if>
            <if test="vo.ticketTimeEnd != null and vo.ticketTimeEnd != ''">
                and ccs.ticket_time &lt;= #{vo.ticketTimeEnd}
            </if>
            <if test="isAdmin != null and isAdmin == 0">
                <if test="deptType != null and deptType == 1 and deptIds != null and deptIds.size > 0">
                    and (ccs.advisor_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="deptType != null and deptType == 2 and deptIds != null and deptIds.size > 0">
                    and (ccs.accounting_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.queryDeptIds != null and vo.queryDeptIds.size > 0">
                <if test="deptType != null and deptType == 1">
                    and (ccs.advisor_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.advisor_top_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="deptType != null and deptType == 2">
                    and (ccs.accounting_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="vo.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.advisorSearchDeptIds != null and vo.advisorSearchDeptIds.size > 0">
                and (ccs.advisor_dept_id in
                <foreach collection="vo.advisorSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or ccs.advisor_top_dept_id in
                <foreach collection="vo.advisorSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>)
            </if>
            <if test="vo.accountingSearchDeptIds != null and vo.accountingSearchDeptIds.size > 0">
                and (ccs.accounting_dept_id in
                <foreach collection="vo.accountingSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or ccs.accounting_top_dept_id in
                <foreach collection="vo.accountingSearchDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>)
            </if>
            <if test="vo.tagName != null and vo.tagName != ''">
                <if test="tagIncludeFlag != null and tagIncludeFlag == 1 and ids != null and ids.size > 0">
                    and ccs.id in
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="tagIncludeFlag != null and tagIncludeFlag == 0 and ids != null and ids.size > 0">
                    and ccs.id not in
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="vo.businessDeptId != null">
                and ccs.business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.accountingTopDeptId != null">
                and ccs.accounting_top_dept_id = #{vo.accountingTopDeptId}
            </if>
            <if test="vo.startPeriodStart != null">
                and ccs.first_account_period >= #{vo.startPeriodStart}
            </if>
            <if test="vo.startPeriodEnd != null">
                and ccs.first_account_period &lt;= #{vo.startPeriodEnd}
            </if>
            <if test="vo.endPeriodStart != null">
                and ccs.end_account_period >= #{vo.endPeriodStart}
            </if>
            <if test="vo.endPeriodEnd != null">
                and ccs.end_account_period &lt;= #{vo.endPeriodEnd}
            </if>
            <if test="vo.advisorDeptId != null">
                and ccs.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and ccs.accounting_dept_id = #{vo.accountingDeptId}
            </if>
        </where>
        group by ccs.advisor_dept_id
    </select>
    <select id="xqySetCustomerStatusList" resultType="com.bxm.customer.domain.CCustomerService">
        SELECT DISTINCT c_customer_service.id,c_customer_service.tax_number
        FROM c_customer_service JOIN c_customer_sys_account ON c_customer_service.id = c_customer_sys_account.customer_service_id AND c_customer_sys_account.is_del = 0 AND c_customer_sys_account.sys_type_name = '鑫启易'
        LEFT JOIN c_customer_service cs ON c_customer_service.tax_number = cs.tax_number AND cs.is_del = 0 AND cs.id != c_customer_service.id AND (cs.service_status = 1 OR cs.end_account_period = #{prePeriod})
        WHERE c_customer_service.is_del = 0 AND c_customer_service.tax_number is not null and c_customer_service.tax_number != '' AND c_customer_service.service_status = 2 AND c_customer_service.end_account_period = #{endPeriod}
          AND cs.id IS null
    </select>
    <select id="xqySetCustomerServiceUserList" resultType="com.bxm.customer.domain.CCustomerService">
        SELECT DISTINCT c_customer_service.id,c_customer_service.tax_number,c_customer_service.accounting_top_dept_id,c_customer_service.accounting_dept_id
        FROM c_customer_service JOIN c_customer_sys_account ON c_customer_service.id = c_customer_sys_account.customer_service_id AND c_customer_sys_account.is_del = 0 AND c_customer_sys_account.sys_type_name = '鑫启易'
        JOIN c_customer_operator_record ON c_customer_service.id = c_customer_operator_record.customer_service_id and c_customer_operator_record.is_done = 0 and c_customer_operator_record.operator_type = 1 and c_customer_operator_record.operator_month = #{period}
        WHERE c_customer_service.is_del = 0 AND c_customer_service.tax_number is not null and c_customer_service.tax_number != '' AND c_customer_service.accounting_dept_id is not null
    </select>
    <select id="repeatCustomerNameCount" resultType="java.lang.Integer">
        select count(1) from (select business_top_dept_id,customer_name,count(*) from c_customer_service ccs where service_status = 1 and is_del = 0 group by business_top_dept_id,customer_name having count(*) > 1) a
    </select>
    <select id="repeatCreditCodeCount" resultType="java.lang.Integer">
        select count(1) from (select business_top_dept_id,customer_name,credit_code,count(*) from c_customer_service ccs where service_status = 1 and is_del = 0 group by business_top_dept_id,customer_name,credit_code having count(*) > 1) a
    </select>
    <select id="repeatTaxNumberCount" resultType="java.lang.Integer">
        select count(1) from (select business_top_dept_id,customer_name,tax_number,count(*) from c_customer_service ccs where service_status = 1 and is_del = 0 group by business_top_dept_id,customer_name,tax_number having count(*) > 1) a
    </select>
    <select id="invalidCustomerMonthCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_period_month ccspm left join c_customer_service ccs on ccspm.customer_service_id = ccs.id where ccs.is_del = 1 or ccs.id is null
    </select>
    <select id="invalidCustomerYearCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_period_year ccspy left join c_customer_service ccs on ccspy.customer_service_id = ccs.id where ccs.is_del = 1 or ccs.id is null
    </select>
    <select id="notSamePeriodCustomerInfoCount" resultType="java.lang.Integer">
        select count(1) from
        c_customer_service_period_month ccspm
        left join c_customer_service ccs on ccspm.customer_service_id = ccs.id and ccs.is_del = 0
        where ccspm.customer_name != ccs.customer_name or ccspm.credit_code != ccs.credit_code
    </select>
    <select id="repeatYearPeriodCustomerIdCount" resultType="java.lang.Integer">
        select count(1) from (select customer_service_id,period,count(*),group_concat(id),group_concat(customer_service_id) from c_customer_service_period_year ccspy group by customer_service_id,period having count(*)>1) a
    </select>
    <select id="repeatMonthPeriodCustomerIdCount" resultType="java.lang.Integer">
        select count(1) from (select business_top_dept_id,customer_service_id,period,count(*),group_concat(id),group_concat(customer_service_id) from c_customer_service_period_month ccspm group by customer_service_id,period,business_top_dept_id having count(*)>1) a
    </select>
    <select id="repeatMonthPeriodCreditCodeCount" resultType="java.lang.Integer">
        select count(1) from (select business_top_dept_id,credit_code ,period,count(*),group_concat(id),group_concat(customer_service_id) from c_customer_service_period_month ccspm group by credit_code,period,business_top_dept_id having count(*)>1) a
    </select>
    <select id="repeatMonthPeriodCustomerNameCount" resultType="java.lang.Integer">
        select count(1) from (select business_top_dept_id,customer_name,period,count(*),group_concat(id),group_concat(customer_service_id) from c_customer_service_period_month ccspm group by customer_name,period,business_top_dept_id having count(*)>1) a
    </select>
    <select id="repeatMonthPeriodTaxNumberCount" resultType="java.lang.Integer">
        select count(1) from (select business_top_dept_id,tax_number,period,count(*),group_concat(id),group_concat(customer_service_id) from c_customer_service_period_month ccspm group by tax_number,period,business_top_dept_id having count(*)>1) a
    </select>
    <select id="noGeneratePeriodCount" resultType="java.lang.Integer">
        select COUNT(1) FROM (WITH monthcount AS (

        -- 子查询：统计每个客户服务的账期信息

        SELECT

        customer_service_id AS ccsid, -- 客户服务 ID

        MIN(period) AS minperiod, -- 账期中的最小值

        MAX(period) AS maxperiod, -- 账期中的最大值

        COUNT(*) AS periodnum -- 账期数量统计

        FROM

        c_customer_service_period_month ccspm -- 来源表：客户服务账期表

        WHERE

        period >= 202401 -- 筛选条件：仅统计 202401（含）及之后的账期

        GROUP BY

        customer_service_id -- 按客户服务 ID 分组统计

        )

        SELECT *

        FROM (

        SELECT

        ccs.customer_company_name, -- 客户名称

        ccs.service_type, -- 服务类型

        CASE

        WHEN CAST(ccs.first_account_period AS UNSIGNED) &lt; 202401 THEN 202401 -- 如果初始账期早于 202401，则调整为 202401

        ELSE CAST(ccs.first_account_period AS UNSIGNED) -- 否则保持原值

        END AS adjusted_first_period, -- 调整后的初始账期

        COALESCE(CAST(ccs.end_account_period AS UNSIGNED), CAST(DATE_FORMAT(NOW(), '%Y%m') AS UNSIGNED)) AS adjusted_end_period,

        -- 调整后的结束账期：如果为空，默认为当前月

        ((FLOOR(COALESCE(CAST(ccs.end_account_period AS UNSIGNED), CAST(DATE_FORMAT(NOW(), '%Y%m') AS UNSIGNED)) / 100) * 12 +

        MOD(COALESCE(CAST(ccs.end_account_period AS UNSIGNED), CAST(DATE_FORMAT(NOW(), '%Y%m') AS UNSIGNED)), 100)) -

        (FLOOR(CASE

        WHEN CAST(ccs.first_account_period AS UNSIGNED) &lt; 202401 THEN 202401

        ELSE CAST(ccs.first_account_period AS UNSIGNED)

        END / 100) * 12 +

        MOD(CASE

        WHEN CAST(ccs.first_account_period AS UNSIGNED) &lt; 202401 THEN 202401

        ELSE CAST(ccs.first_account_period AS UNSIGNED)

        END, 100)) + 1) AS total_account_periods, -- 修正的总账期数

        COALESCE(monthcount.periodnum, 0) AS periodnum, -- 账期表中统计的账期数量（默认为 0）

        monthcount.minperiod, -- 账期中的最小账期

        monthcount.maxperiod -- 账期中的最大账期

        FROM

        c_customer_service ccs -- 主表：客户服务表

        LEFT JOIN

        monthcount

        ON ccs.id = monthcount.ccsid -- 将主表与账期统计结果表连接

        WHERE

        ccs.is_del = 0 -- 筛选条件：仅保留未删除的客户服务记录

        ) AS filtered_data

        WHERE

        total_account_periods > periodnum -- 新的过滤条件：总账期数大于统计的账期数量

        ORDER BY

        customer_company_name) a
    </select>
    <select id="repeatTagCustomerCount" resultType="java.lang.Integer">
        select count(1) from (select business_id ,tag_id, count(*) from c_business_tag_relation cbtr left join c_customer_service ccs on cbtr.business_id = ccs.id where business_type = 1 and ccs.is_del= 0 group by business_id ,tag_id having count(*) >1) a
    </select>
    <select id="repeatTagMonthPeriodCount" resultType="java.lang.Integer">
        select count(1) from (select business_id ,tag_id, count(*) from c_business_tag_relation cbtr left join c_customer_service_period_month ccspm on cbtr.business_id = ccspm.id where business_type = 2 and ccspm.id is not null group by business_id ,tag_id having count(*) >1) a
    </select>
    <select id="repeatTaxDeliverCount" resultType="java.lang.Integer">
        select count(1) from (select customer_service_period_month_id ,deliver_type ,period, count(*) from c_customer_deliver ccd where is_del = 0 group by customer_service_period_month_id ,deliver_type ,period having count(*) >1) a
    </select>
    <select id="invalidTaxDeliverPeriodCount" resultType="java.lang.Integer">
        select count(1) from c_customer_deliver ccd

                                 left join c_customer_service_period_month ccspm on ccd.customer_service_period_month_id = ccspm.id

                                 left join c_customer_service ccs on ccd.customer_service_id = ccs.id and ccs.is_del= 0

        where ccd.is_del = 0 AND (ccspm.id is null or ccs.id is NULL)
    </select>
    <select id="repeatIncomeDeliverCount" resultType="java.lang.Integer">
        select count(1) from (select customer_service_period_month_id,period, count(*) from c_customer_service_in_account ccsia where is_del = 0 group by customer_service_period_month_id ,period having count(*) >1) a
    </select>
    <select id="invalidIncomeDeliverPeriodCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_in_account ccsia

                                                                                                                                left join c_customer_service_period_month ccspm on ccsia.customer_service_period_month_id = ccspm.id

                                                                                                                                left join c_customer_service ccs on ccsia.customer_service_id = ccs.id and ccs.is_del= 0

                              where ccsia.is_del = 0 and (ccspm.id is null or ccs.id is null)
    </select>
    <select id="repeatIncomeCustomerNameCount" resultType="java.lang.Integer">
        select count(1) from (select ccs.id, ccs.customer_name ,ccspmi.period,group_concat(ccspmi.id),count(*) from c_customer_service_period_month_income ccspmi left join c_customer_service ccs on ccspmi.customer_service_id = ccs.id

                              group by ccs.id, ccs.customer_name ,ccspmi.period having count(*) > 1) a
    </select>
    <select id="repeatIncomeCreditCodeCount" resultType="java.lang.Integer">
        select count(1) from (select ccs.id, ccs.credit_code ,ccspmi.period,group_concat(ccspmi.id),count(*) from c_customer_service_period_month_income ccspmi left join c_customer_service ccs on ccspmi.customer_service_id = ccs.id

                              group by ccs.id, ccs.credit_code ,ccspmi.period having count(*) > 1) a
    </select>
    <select id="notSameIncomeDeliverCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_in_account ccsia where (deliver_result = 1 and in_account_result != 1) or (deliver_result = 2 and in_account_result != 3) or (deliver_result = 3 and in_account_result != 2) or (deliver_result = 4 and in_account_result != 4)
    </select>
    <select id="inResultNotNullAndInResultIsNullIncomeDeliverCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_in_account ccsia where (in_time is not null and in_account_result is null and is_del = 0) or (in_time is null and in_account_result is not null and in_account_result !=2 and is_del = 0)
    </select>
    <select id="bankTimeNotNullAndBankResultIsNullIncomeDeliverCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_in_account ccsia where bank_payment_input_time is not null and bank_payment_input_result is null and is_del = 0
    </select>
    <select id="bankTimeNotNullAndBankResultIsErrorOrPartIncomeDeliverCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_in_account ccsia where bank_payment_input_time is not null and bank_payment_input_result in (2,6) and is_del = 0
    </select>
    <select id="bankResultNotNullAndBankResultIsNotErrorOrPartAndBankTimeIsNullIncomeDeliverCount"
            resultType="java.lang.Integer">
        select count(1) from c_customer_service_in_account ccsia where bank_payment_input_time is null and bank_payment_input_result is not null and bank_payment_input_result not in (2,6) and is_del = 0
    </select>
    <select id="incomePeriodIsNullCount" resultType="java.lang.Integer">
        select count(1) from (
                                 SELECT
                                     ccs.id,
                                     ccs.customer_name,
                                     COUNT(CASE WHEN ccspmi.id IS NOT NULL THEN 1 ELSE NULL END) AS income_count
                                 FROM
                                     c_customer_service ccs
                                         LEFT JOIN
                                     c_customer_service_period_month_income ccspmi
                                     ON ccs.id = ccspmi.customer_service_id
                                         AND ccspmi.period >= EXTRACT(YEAR_MONTH FROM DATE_SUB(NOW(), INTERVAL 1 YEAR)) -- 按数字比较
                                 WHERE
                                     ccs.service_status = 1
                                   AND ccs.is_del = 0
                                 GROUP BY
                                     ccs.id, ccs.customer_name
                                 HAVING
                                     income_count != 13
                             ) a
    </select>
    <select id="noPeriodYear" resultType="java.lang.Integer">
        select count(1) from c_customer_service ccs left join c_customer_service_period_year ccspy on ccs.id = ccspy.customer_service_id and ccspy.period = #{year} where ccs.is_del= 0 and (ccs.end_account_period is null or ccs.end_account_period > #{yearFirstPeriod}) and ccspy.id is null
    </select>
    <select id="selectNoBankCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM c_customer_service
        WHERE c_customer_service.is_del = 0
          AND (c_customer_service.service_status = 1
            OR (c_customer_service.service_status = 2 AND c_customer_service.end_account_period = #{prePeriod}))
          AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_bank_account
            WHERE c_customer_service.id = c_customer_service_bank_account.customer_service_id
        )
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (advisor_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or accounting_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="getCustomerPeriodBankList"
            resultType="com.bxm.customer.api.domain.dto.RemoteCustomerPeriodBankDTO">
        SELECT
            c_customer_service.id AS customerServiceId,
            c_customer_service.customer_name AS customerServiceName,
            c_customer_service_bank_account.bank_account_number AS bankAccountNumber,
            c_customer_service_bank_account.bank_name as bankName,
            c_customer_service_period_month.id AS customerServicePeriodMonthId,
            c_customer_service_period_month.period AS period
        FROM c_customer_service_bank_account JOIN
             c_customer_service ON c_customer_service_bank_account.customer_service_id = c_customer_service.id AND c_customer_service.is_del = 0
                                             JOIN c_customer_service_period_month ON c_customer_service.id = c_customer_service_period_month.customer_service_id
        WHERE c_customer_service_bank_account.bank_account_number IN
        <foreach collection="bankAccountNumbers" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        AND (c_customer_service_bank_account.account_open_date IS NULL OR c_customer_service_period_month.period &gt;= DATE_FORMAT(c_customer_service_bank_account.account_open_date, '%Y%m'))
        AND (c_customer_service_bank_account.account_close_date IS NULL OR c_customer_service_period_month.period &lt;= DATE_FORMAT(c_customer_service_bank_account.account_close_date, '%Y%m'))
    </select>
    <select id="getCustomerBankListByCustomerServiceIds"
            resultType="com.bxm.customer.api.domain.dto.RemoteCustomerBankAccountDTO">
        SELECT
        c_customer_service.id AS customerServiceId,
        c_customer_service.customer_name AS customerServiceName,
        c_customer_service_bank_account.bank_account_number AS bankAccountNumber,
        c_customer_service_bank_account.bank_name as bankName,
        c_customer_service_period_month.id AS customerServicePeriodMonthId,
        c_customer_service_period_month.period AS period,
        c_customer_service_bank_account.account_open_date as accountOpenDate,
        c_customer_service_bank_account.account_close_date as accountCloseDate
        FROM c_customer_service_bank_account JOIN
        c_customer_service ON c_customer_service_bank_account.customer_service_id = c_customer_service.id AND c_customer_service.is_del = 0
        JOIN c_customer_service_period_month ON c_customer_service.id = c_customer_service_period_month.customer_service_id
        WHERE c_customer_service.id IN
        <foreach collection="customerServiceIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        AND (c_customer_service_bank_account.account_open_date IS NULL OR c_customer_service_period_month.period &gt;= DATE_FORMAT(c_customer_service_bank_account.account_open_date, '%Y%m'))
        AND (c_customer_service_bank_account.account_close_date IS NULL OR c_customer_service_period_month.period &lt;= DATE_FORMAT(c_customer_service_bank_account.account_close_date, '%Y%m'))
    </select>
    <select id="getCustomerPeriodBankListByCustomerServiceIds"
            resultType="com.bxm.customer.api.domain.dto.RemoteCustomerPeriodBankDTO">

    </select>
    <select id="selectByPeriodDataScope" resultType="com.bxm.customer.domain.CCustomerService">
        select distinct cs.*
            from c_customer_service cs join c_customer_service_period_month cspm on cs.id = cspm.customer_service_id
        where cs.is_del = 0
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (cspm.advisor_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or cspm.advisor_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (cspm.accounting_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or cspm.accounting_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="keyWord != null and keyWord != ''">
            and cs.customer_name like concat('%',#{keyWord},'%')
        </if>
        limit 20
    </select>
    <select id="selectInAccountResultNotSameCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_cashier_accounting ccsca
         left join c_customer_service_period_month ccspm on ccsca.customer_service_period_month_id = ccspm.id and is_del = 0
        where ((ccsca.deliver_status != ccspm.in_account_status and ccsca.deliver_status != 6) or (ccsca.deliver_status = 6 and ccspm.in_account_status != 1))  and ccsca.id is not null and ccsca.`type` = 1
    </select>
    <select id="inAccountNotEndAccountWrongCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_period_month ccspm where bank_payment_result not in (0,5,6,7,8) and in_account_status = 2 and settle_account_status != 2
    </select>
    <select id="endSettleAccountStatusWrongCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_period_month ccspm where bank_payment_result in (0,5,6,7,8) and in_account_status in (2) and settle_account_status != 3
    </select>
    <select id="notInAccountNotEndAccountWrongCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_period_month ccspm where in_account_status not in (2) and settle_account_status != 1
    </select>
    <select id="selectBankPaymentResultNotSameCount" resultType="java.lang.Integer">
        select count(1)

        from c_customer_service_cashier_accounting ccsca

                 left join c_customer_service_period_month ccspm on ccsca.customer_service_period_month_id = ccspm.id and ccsca.type = 1 and ccsca.is_del = 0

        where ccsca.bank_payment_result != ccspm.bank_payment_result and ccsca.id is not null and ccsca.type = 1 and ccsca.is_del = 0
    </select>
    <select id="selectSettleAccountResultNotSameCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_cashier_accounting ccsca

                                                         left join c_customer_service_period_month ccspm on ccsca.customer_service_period_month_id = ccspm.id

        where ccsca.settle_account_status != ccspm.settle_account_status and ccsca.id is not null and ccsca.type = 1 and ccsca.is_del = 0
    </select>
    <select id="selectEndCustomerServiceBatchByBusinessTopDeptIdAndCreditCode"
            resultType="com.bxm.customer.domain.CCustomerService">
        select business_top_dept_id, credit_code, service_number from c_customer_service
        where (business_top_dept_id, credit_code) in
        <foreach collection="voList" item="item" close=")" open="(" separator=",">
            (#{item.businessTopDeptId},#{item.creditCode})
        </foreach>
        and is_del = 0 and service_status = 2
        order by end_account_period desc
    </select>
    <select id="accountAreaIdNotSameCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM (
                                 select

                                     ccspm.accounting_top_dept_id atdi,

                                     case WHEN sd.ancestors REGEXP '^[^,]+,[^,]+,' THEN SUBSTRING_INDEX(SUBSTRING_INDEX(sd.ancestors, ',', 3), ',', -1) ELSE null END AS sdlevel2

                                 from c_customer_service_period_month ccspm

                                          left join sys_dept sd on ccspm.accounting_dept_id = sd.dept_id

                                 where accounting_dept_id is NOT NULL) a WHERE a.atdi != a.sdlevel2
    </select>
    <select id="wrongAccountingTopDeptCustomerCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_period_month ccspm where accounting_top_dept_id not in (2,277) and business_top_dept_id in (59,266)
    </select>
    <select id="noAccountingDeptPeriodCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_period_month ccspm where accounting_dept_id is null and period > 202312 and period &lt; #{period}
    </select>
    <select id="noAccountingTopDeptCustomerCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service ccs where accounting_top_dept_id is null and is_del = 0
    </select>
    <select id="noAccountingDeptNoWaitItemCustomerCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service ccs
        left join c_customer_service_wait_item ccswi on ccs.id = ccswi.customer_service_id and ccswi.item_type in (1,2) and ccswi.done_status = 0 and ccswi.is_valid = 1
        where ccs.accounting_dept_id is null and ccs.first_account_period &lt; #{period} and ccs.is_del = 0 and ccs.accounting_top_dept_id = 2 and ccswi.id is null
    </select>
    <select id="customerServiceIncomeInfo"
            resultType="com.bxm.customer.domain.dto.CustomerServiceIncomeInfoDTO">
        select
            cs.id as id,
            cs.customer_name as customerName,
            cs.customer_company_name as customerCompanyName,
            cs.credit_code as creditCode,
            cs.tax_type as taxType,
            cs.business_dept_id as businessDeptId,
            cs.advisor_dept_id as advisorDeptId,
            cs.accounting_top_dept_id as accountingTopDeptId,
            cs.accounting_dept_id as accountingDeptId,
            cs.this_12_month_income as this12MonthIncome,
            cs.this_12_month_income_rpa_result as this12MonthIncomeRpaResult,
            cs.this_year_income as thisYearIncome,
            cs.this_year_income_rpa_result as thisYearIncomeRpaResult,
            cs.this_season_income as thisSeasonIncome,
            cs.this_season_income_rpa_result as thisSeasonIncomeRpaResult
            from c_customer_service cs
        <where>
            cs.is_del = 0
            <if test="vo.tagIncludeFlag != null">
                <if test="vo.tagIncludeFlag == 1">
                    <if test="vo.tagName != null and vo.tagName != '' and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                        and cs.id in
                        <foreach collection="tagCustomerServiceIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                    </if>
                    <if test="vo.tagName != null and vo.tagName != '' and (tagCustomerServiceIds == null or tagCustomerServiceIds.size == 0)">
                        and 1 = 0
                    </if>
                </if>
                <if test="vo.tagIncludeFlag == 0">
                    <if test="vo.tagName != null and vo.tagName != '' and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                        and cs.id not in
                        <foreach collection="tagCustomerServiceIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                    </if>
                </if>
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                <if test="searchCustomerServiceIds != null and searchCustomerServiceIds.size > 0">
                    and cs.id in
                    <foreach collection="searchCustomerServiceIds" separator="," item="id" close=")" open="(">
                        #{id}
                    </foreach>
                </if>
                <if test="searchCustomerServiceIds == null or searchCustomerServiceIds.size == 0">
                    and 1 = 0
                </if>
            </if>
            <if test="userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="userDeptDTO.deptType == 1">
                        and (
                            cs.advisor_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                                #{id}
                            </foreach>
                            or
                            cs.advisor_dept_id in
                            <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                                #{id}
                            </foreach>
                        )
                    </if>
                    <if test="userDeptDTO.deptType == 2">
                        and (
                        cs.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or
                        cs.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                </if>
                <if test="userDeptDTO.deptIds == null or userDeptDTO.deptIds.size == 0">
                    and 1 = 0
                </if>
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (cs.customer_name like concat('%',#{vo.keyWord},'%') or cs.credit_code = #{vo.keyWord})
            </if>
            <if test="vo.serviceStatus != null">
                and cs.service_status = #{vo.serviceStatus}
            </if>
            <if test="vo.taxType != null">
                and cs.tax_type = #{vo.taxType}
            </if>
            <if test="vo.businessDeptId != null">
                and cs.business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.customerServiceAdvisorDeptIds != null and vo.customerServiceAdvisorDeptIds.size > 0">
                and cs.advisor_dept_id in
                <foreach collection="vo.customerServiceAdvisorDeptIds" separator="," item="id" close=")" open="(">
                    #{id}
                </foreach>
            </if>
            <if test="vo.accountingTopDeptId != null">
                and cs.accounting_top_dept_id = #{vo.accountingTopDeptId}
            </if>
            <if test="vo.customerServiceAccountingDeptIds != null and vo.customerServiceAccountingDeptIds.size > 0">
                and cs.accounting_dept_id in
                <foreach collection="vo.customerServiceAccountingDeptIds" separator="," item="id" close=")" open="(">
                    #{id}
                </foreach>
            </if>
            <if test="vo.this12MonthError != null">
                <if test="vo.this12MonthError == 1">
                    and cs.this_12_month_income_rpa_result = 2
                </if>
                <if test="vo.this12MonthError == 0">
                    and cs.this_12_month_income_rpa_result = 1
                </if>
            </if>
            <if test="vo.profitGetTimeStart != null and vo.profitGetTimeStart != '' and vo.profitGetTimeEnd != null and vo.profitGetTimeEnd != ''">
                and exists (
                select 1 from c_customer_service_period_month_income csi1 join (
                select max(csi.`period`) as maxPeriod,csi.customer_service_id from c_customer_service_period_month_income csi
                where csi.customer_service_id = cs.id and csi.rpa_time is not null and csi.`period` between #{periodStart} and #{periodEnd}) a on csi1.customer_service_id = a.customer_service_id and csi1.`period` = a.maxPeriod
                where csi1.rpa_time is not null and csi1.rpa_time between #{vo.profitGetTimeStart} and #{vo.profitGetTimeEnd}
                )
            </if>
        </where>
        order by cs.id desc
    </select>
    <select id="customerServiceYearIncomeInfo"
            resultType="com.bxm.customer.domain.dto.CustomerServiceYearIncomeInfoDTO">
        select
            csy.id as id,
            cs.id as customerServiceId,
            cs.customer_name as customerName,
            cs.customer_company_name as customerCompanyName,
            cs.credit_code as creditCode,
            cs.tax_type as taxType,
            cs.business_dept_id as businessDeptId,
            cs.advisor_dept_id as advisorDeptId,
            cs.accounting_top_dept_id as accountingTopDeptId,
            cs.accounting_dept_id as accountingDeptId,
            csy.`period` as `period`
        from c_customer_service_period_year csy join c_customer_service cs on csy.customer_service_id = cs.id and cs.is_del = 0
        <where>
            <if test="vo.tagIncludeFlag != null">
                <if test="vo.tagIncludeFlag == 1">
                    <if test="vo.tagName != null and vo.tagName != '' and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                        and cs.id in
                        <foreach collection="tagCustomerServiceIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                    </if>
                    <if test="vo.tagName != null and vo.tagName != '' and (tagCustomerServiceIds == null or tagCustomerServiceIds.size == 0)">
                        and 1 = 0
                    </if>
                </if>
                <if test="vo.tagIncludeFlag == 0">
                    <if test="vo.tagName != null and vo.tagName != '' and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                        and cs.id not in
                        <foreach collection="tagCustomerServiceIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                    </if>
                </if>
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                <if test="searchCustomerServiceIds != null and searchCustomerServiceIds.size > 0">
                    and cs.id in
                    <foreach collection="searchCustomerServiceIds" separator="," item="id" close=")" open="(">
                        #{id}
                    </foreach>
                </if>
                <if test="searchCustomerServiceIds == null or searchCustomerServiceIds.size == 0">
                    and 1 = 0
                </if>
            </if>
            <if test="userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="userDeptDTO.deptType == 1">
                        and (
                        cs.advisor_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or
                        cs.advisor_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                    <if test="userDeptDTO.deptType == 2">
                        and (
                        cs.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or
                        cs.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                </if>
                <if test="userDeptDTO.deptIds == null or userDeptDTO.deptIds.size == 0">
                    and 1 = 0
                </if>
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (cs.customer_name like concat('%',#{vo.keyWord},'%') or cs.credit_code = #{vo.keyWord})
            </if>
            <if test="vo.serviceStatus != null">
                and cs.service_status = #{vo.serviceStatus}
            </if>
            <if test="vo.taxType != null">
                and cs.tax_type = #{vo.taxType}
            </if>
            <if test="vo.businessDeptId != null">
                and cs.business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.customerServiceAdvisorDeptIds != null and vo.customerServiceAdvisorDeptIds.size > 0">
                and cs.advisor_dept_id in
                <foreach collection="vo.customerServiceAdvisorDeptIds" separator="," item="id" close=")" open="(">
                    #{id}
                </foreach>
            </if>
            <if test="vo.accountingTopDeptId != null">
                and cs.accounting_top_dept_id = #{vo.accountingTopDeptId}
            </if>
            <if test="vo.customerServiceAccountingDeptIds != null and vo.customerServiceAccountingDeptIds.size > 0">
                and cs.accounting_dept_id in
                <foreach collection="vo.customerServiceAccountingDeptIds" separator="," item="id" close=")" open="(">
                    #{id}
                </foreach>
            </if>
            <if test="vo.year != null">
                and csy.`period` = #{vo.year}
            </if>
            <if test="vo.hasError != null">
                <if test="vo.hasError == 1">
                    and exists (
                        select 1 from c_customer_service_period_month_income spm
                        where spm.customer_service_id = csy.customer_service_id
                        and floor(spm.period / 100) = csy.`period` and spm.rpa_result = 2
                    )
                </if>
                <if test="vo.hasError == 0">
                    and not exists (
                    select 1 from c_customer_service_period_month_income spm
                    where spm.customer_service_id = csy.customer_service_id
                    and floor(spm.period / 100) = csy.`period` and spm.rpa_result = 2
                    )
                </if>
            </if>
            <if test="vo.profitGetTimeStart != null and vo.profitGetTimeStart != '' and vo.profitGetTimeEnd != null and vo.profitGetTimeEnd != ''">
                and exists (
                select 1 from c_customer_service_period_month_income csi1 join (
                select max(csi.`period`) as maxPeriod,csi.customer_service_id from c_customer_service_period_month_income csi
                where csi.customer_service_id = cs.id and csi.rpa_time is not null and csi.`period` between csy.`period` * 100 + 1 and csy.`period` * 100 + 12) a on csi1.customer_service_id = a.customer_service_id and csi1.`period` = a.maxPeriod
                where csi1.rpa_time is not null and csi1.rpa_time between #{vo.profitGetTimeStart} and #{vo.profitGetTimeEnd}
                )
            </if>
        </where>
        ORDER BY csy.period DESC,cs.id desc
    </select>
    <select id="customerServiceIncomeAdvisorDeptCount" resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        cs.advisor_dept_id as deptId,
        count(1) as dataCount
        from c_customer_service cs
        <where>
            cs.is_del = 0 and cs.advisor_dept_id is not null
            <if test="userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="userDeptDTO.deptType == 1">
                        and (
                        cs.advisor_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or
                        cs.advisor_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                    <if test="userDeptDTO.deptType == 2">
                        and (
                        cs.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or
                        cs.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                </if>
                <if test="userDeptDTO.deptIds == null or userDeptDTO.deptIds.size == 0">
                    and 1 = 0
                </if>
            </if>
        </where>
        group by cs.advisor_dept_id
    </select>
    <select id="customerServiceIncomeAccountingDeptCount" resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        cs.accounting_dept_id as deptId,
        count(1) as dataCount
        from c_customer_service cs
        <where>
            cs.is_del = 0 and cs.accounting_dept_id is not null
            <if test="userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="userDeptDTO.deptType == 1">
                        and (
                        cs.advisor_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or
                        cs.advisor_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                    <if test="userDeptDTO.deptType == 2">
                        and (
                        cs.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or
                        cs.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                </if>
                <if test="userDeptDTO.deptIds == null or userDeptDTO.deptIds.size == 0">
                    and 1 = 0
                </if>
            </if>
        </where>
        group by cs.accounting_dept_id
    </select>
    <select id="customerServiceYearIncomeAdvisorDeptCount" resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        cs.advisor_dept_id as deptId,
        count(1) as dataCount
        from c_customer_service_period_year csy join c_customer_service cs on csy.customer_service_id = cs.id and cs.is_del = 0
        <where>
            cs.advisor_dept_id is not null
            <if test="userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="userDeptDTO.deptType == 1">
                        and (
                        cs.advisor_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or
                        cs.advisor_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                    <if test="userDeptDTO.deptType == 2">
                        and (
                        cs.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or
                        cs.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                </if>
                <if test="userDeptDTO.deptIds == null or userDeptDTO.deptIds.size == 0">
                    and 1 = 0
                </if>
            </if>
        </where>
        group by cs.advisor_dept_id
    </select>
    <select id="customerServiceYearIncomeAccountingDeptCount" resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        cs.accounting_dept_id as deptId,
        count(1) as dataCount
        from c_customer_service_period_year csy join c_customer_service cs on csy.customer_service_id = cs.id and cs.is_del = 0
        <where>
            cs.accounting_dept_id is not null
            <if test="userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="userDeptDTO.deptType == 1">
                        and (
                        cs.advisor_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or
                        cs.advisor_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                    <if test="userDeptDTO.deptType == 2">
                        and (
                        cs.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or
                        cs.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                </if>
                <if test="userDeptDTO.deptIds == null or userDeptDTO.deptIds.size == 0">
                    and 1 = 0
                </if>
            </if>
        </where>
        group by cs.accounting_dept_id
    </select>
    <select id="noAdvisorCount" resultType="java.lang.Integer">
        select count(1) from c_customer_service_period_month ccspm where ccspm.advisor_dept_id is null and period &gt; 202312 and period &lt; #{period}
    </select>
    <select id="errorSettleAccountStatusCount" resultType="java.lang.Integer">
        SELECT COUNT(sspm.id) from c_settlement_order_data sd
                                       JOIN c_customer_service_period_month sspm ON sd.business_id = sspm.id
        WHERE EXISTS (SELECT 1 FROM c_settlement_order so WHERE so.id = sd.settlement_order_id AND so.`status` = 5 AND so.is_del = 0 AND so.settlement_type = 1 AND so.is_supplement = 0)
          AND sspm.settlement_status != 4
    </select>
    <select id="selectAccountingStatisticList" resultType="com.bxm.customer.domain.CCustomerService">
        select
            id,
            accounting_dept_id
            from c_customer_service
        where is_del = 0
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            and (accounting_top_dept_id in
            <foreach collection="userDept.deptIds" separator="," item="id" close=")" open="(">
                #{id}
            </foreach>
            or accounting_dept_id in
            <foreach collection="userDept.deptIds" separator="," item="id" close=")" open="(">
                #{id}
            </foreach>
            )
        </if>
        <if test="waitDispatchIds != null and waitDispatchIds.size > 0">
            and (accounting_dept_id is null or (accounting_dept_id is not null and id in
                <foreach collection="waitDispatchIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            ))
        </if>
        <if test="waitDispatchIds == null or waitDispatchIds.size == 0">
            and accounting_dept_id is null
        </if>
    </select>
    <select id="selectNoAdvisorDeptCount" resultType="java.lang.Long">
        select count(1)
        from c_customer_service
        where is_del = 0 and advisor_dept_id is null
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            and advisor_top_dept_id in
            <foreach collection="userDept.deptIds" separator="," item="id" close=")" open="(">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="customerServiceWaitDispatchList"
            resultType="com.bxm.customer.domain.dto.CustomerServiceWaitItemDTO">
        select
            cs.id as id,
            cs.id as customerServiceId,
            cs.customer_name as customerName,
            cs.customer_company_name as customerCompanyName,
            cs.service_number as serviceNumber,
            cs.tax_type as taxType,
            sd1.dept_name as businessDeptName,
            sd2.dept_id as advisorDeptId,
            sd2.dept_name as advisorDeptName,
            sd3.dept_id as accountingDeptId,
            sd3.dept_name as accountingDeptName,
            cs.business_dept_id as businessDeptId,
            cs.business_top_dept_id as businessTopDeptId,
            cs.accounting_top_dept_id as accountingTopDeptId,
            sd4.dept_name as accountingTopDeptName
        from c_customer_service cs
          left join sys_dept sd1 on cs.business_dept_id = sd1.dept_id
          left join sys_dept sd2 on cs.advisor_dept_id = sd2.dept_id
          left join sys_dept sd3 on cs.accounting_dept_id = sd3.dept_id
          left join sys_dept sd4 on cs.accounting_top_dept_id = sd4.dept_id
        <where>
            cs.is_del = 0
            <if test="itemType != null">
                <if test="itemType == 1">
                    and cs.accounting_dept_id is null
                    <if test="deptIds != null and deptIds.size > 0">
                        and cs.accounting_top_dept_id in
                        <foreach collection="deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="itemType == 2">
                    and cs.accounting_dept_id is not null
                    <if test="deptIds != null and deptIds.size > 0">
                        and (cs.accounting_top_dept_id in
                        <foreach collection="deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or cs.accounting_dept_id in
                        <foreach collection="deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                    and exists (
                        select 1 from c_customer_service_wait_item where customer_service_id = cs.id
                        and item_type = 1 and is_valid = 1 and done_status = 0
                    )
                </if>
                <if test="itemType == 3">
                    and cs.advisor_dept_id is null
                    <if test="deptIds != null and deptIds.size > 0">
                        and cs.advisor_top_dept_id in
                        <foreach collection="deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                    </if>
                </if>
            </if>
            <if test="keyWord != null and keyWord != ''">
                and (cs.customer_name like concat('%',#{keyWord},'%') or cs.credit_code = #{keyWord})
            </if>
            <if test="tagName != null and tagName != ''">
                <if test="tagIncludeFlag == 1">
                    <if test="customerServiceIds != null and customerServiceIds.size > 0">
                        and cs.id in
                        <foreach collection="customerServiceIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagIncludeFlag == 0">
                    <if test="customerServiceIds != null and customerServiceIds.size > 0">
                        and cs.id not in
                        <foreach collection="customerServiceIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                    </if>
                </if>
            </if>
            <if test="taxType != null">
                and cs.tax_type = #{taxType}
            </if>
        </where>
        order by cs.id desc
    </select>
    <select id="selectItemCount" resultType="java.lang.Long">
        select
        count(cs.id)
        from c_customer_service cs
        <where>
            cs.is_del = 0
            <if test="itemType != null">
                <if test="itemType == 1">
                    and cs.accounting_dept_id is null
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and cs.accounting_top_dept_id in
                        <foreach collection="userDept.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="itemType == 2">
                    and cs.accounting_dept_id is not null
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (cs.accounting_top_dept_id in
                        <foreach collection="userDept.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        or cs.accounting_dept_id in
                        <foreach collection="userDept.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                        )
                    </if>
                    and exists (
                    select 1 from c_customer_service_wait_item where customer_service_id = cs.id
                    and item_type = 1 and is_valid = 1 and done_status = 0
                    )
                </if>
                <if test="itemType == 3">
                    and cs.advisor_dept_id is null
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and cs.advisor_top_dept_id in
                        <foreach collection="userDept.deptIds" separator="," item="id" close=")" open="(">
                            #{id}
                        </foreach>
                    </if>
                </if>
            </if>
        </where>
    </select>

    <insert id="saveCreateOperateLog">
        insert into sys_business_log
        select null,1,id,'新增',1,'系统','','','','','',now(),'',now()
        from c_customer_service where id in
            <foreach collection="customerServiceIds" separator="," item="id" close=")" open="(">
                #{id}
            </foreach>
    </insert>
</mapper>