package com.bxm.customer.service.strategy;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ValueAddedAbstractStatusChangeStrategy 测试类
 * 
 * 验证抽象基类的公共验证方法是否正常工作
 * 
 * <AUTHOR>
 * @date 2025-08-16
 */
public class ValueAddedAbstractStatusChangeStrategyTest {

    private TestValueAddedChangeStrategy testStrategy;
    private ValueAddedDeliveryOrder order;
    private StatusChangeRequestDTO request;

    @BeforeEach
    void setUp() {
        testStrategy = new TestValueAddedChangeStrategy();
        order = new ValueAddedDeliveryOrder();
        request = new StatusChangeRequestDTO();
        request.setDeliveryOrderNo("TEST001");
        request.setTargetStatus("COMPLETED");
    }

    @Test
    void testValidateOperatorId_Success() {
        // 测试操作人ID验证成功
        assertDoesNotThrow(() -> testStrategy.testValidateOperatorId(1L));
    }

    @Test
    void testValidateOperatorId_Null() {
        // 测试操作人ID为空时抛出异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> testStrategy.testValidateOperatorId(null)
        );
        assertEquals("操作人ID不能为空", exception.getMessage());
    }

    @Test
    void testValidateReason_Success() {
        // 测试原因验证成功
        assertDoesNotThrow(() -> testStrategy.testValidateReason("这是一个足够长的原因说明", 10, "测试"));
    }

    @Test
    void testValidateReason_TooShort() {
        // 测试原因长度不足时抛出异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> testStrategy.testValidateReason("短", 10, "测试")
        );
        assertEquals("测试原因描述不能少于10个字符", exception.getMessage());
    }

    @Test
    void testValidateNotEmpty_Success() {
        // 测试非空验证成功
        assertDoesNotThrow(() -> testStrategy.testValidateNotEmpty("有内容", "测试字段"));
    }

    @Test
    void testValidateNotEmpty_Null() {
        // 测试空值时抛出异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> testStrategy.testValidateNotEmpty(null, "测试字段")
        );
        assertEquals("测试字段不能为空", exception.getMessage());
    }

    @Test
    void testValidateNotEmpty_Empty() {
        // 测试空字符串时抛出异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> testStrategy.testValidateNotEmpty("   ", "测试字段")
        );
        assertEquals("测试字段不能为空", exception.getMessage());
    }

    @Test
    void testValidateCustomerInfo_Success() {
        // 测试客户信息验证成功
        order.setCustomerId(1L);
        assertDoesNotThrow(() -> testStrategy.testValidateCustomerInfo(order));
    }

    @Test
    void testValidateCustomerInfo_Null() {
        // 测试客户ID为空时抛出异常
        order.setCustomerId(null);
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> testStrategy.testValidateCustomerInfo(order)
        );
        assertEquals("客户ID不能为空", exception.getMessage());
    }

    /**
     * 测试用的具体策略实现类
     */
    private static class TestValueAddedChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

        @Override
        public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
            return true;
        }

        @Override
        protected void validateSpecificTransition(ValueAddedDeliveryOrder order, 
                                                StatusChangeRequestDTO request, 
                                                ValueAddedDeliveryOrderStatus targetStatus) {
            // 测试实现，不做具体验证
        }

        @Override
        public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
            return ValueAddedDeliveryOrderStatus.DRAFT;
        }

        // 公开受保护的方法用于测试
        public void testValidateOperatorId(Long operatorId) {
            validateOperatorId(operatorId);
        }

        public void testValidateReason(String reason, int minLength, String context) {
            validateReason(reason, minLength, context);
        }

        public void testValidateNotEmpty(String value, String fieldName) {
            validateNotEmpty(value, fieldName);
        }

        public void testValidateCustomerInfo(ValueAddedDeliveryOrder order) {
            validateCustomerInfo(order);
        }
    }
}
